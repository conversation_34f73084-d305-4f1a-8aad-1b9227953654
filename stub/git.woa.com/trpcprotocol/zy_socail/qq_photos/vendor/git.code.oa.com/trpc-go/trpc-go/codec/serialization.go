package codec

import (
	"errors"

	"github.com/spf13/cast"
)

// Serializer defines body serialization interface.
type Serializer interface {
	// Unmarshal deserialize the in bytes into body
	Unmarshal(in []byte, body interface{}) error

	// Marshal returns the bytes serialized from body.
	Marshal(body interface{}) (out []byte, err error)
}

// SerializationType defines the code of different serializers, such as
// protobuf, jce, json, http-get-query and http-get-restful.
//
//   - code 0-127 is used for common modes in all language versions of trpc.
//   - code 128-999 is used for modes in any language specific version of trpc.
//   - code 1000+ is used for customized occasions in which conflicts should
//     be avoided.
const (
	// SerializationTypePB is protobuf serialization code.
	SerializationTypePB = 0
	// SerializationTypeJCE is jce serialization code.
	SerializationTypeJCE = 1
	// SerializationTypeJSON is json serialization code.
	SerializationTypeJSON = 2
	// SerializationTypeFlatBuffer is flatbuffer serialization code.
	SerializationTypeFlatBuffer = 3
	// SerializationTypeNoop is bytes empty serialization code.
	SerializationTypeNoop = 4
	// SerializationTypeXML is xml serialization code (application/xml for http).
	SerializationTypeXML = 5
	// SerializationTypeThriftBinary is thrift binary protocol serialization code.
	SerializationTypeThriftBinary = 6
	// SerializationTypeThriftCompact is thrift compact protocol serialization code.
	SerializationTypeThriftCompact = 7
	// SerializationTypeTextXML is xml serialization code (text/xml for http).
	SerializationTypeTextXML = 8

	// SerializationTypeUnsupported is unsupported serialization code.
	SerializationTypeUnsupported = 128
	// SerializationTypeForm is used to handle form request.
	SerializationTypeForm = 129
	// SerializationTypeGet is used to handle http get request.
	SerializationTypeGet = 130
	// SerializationTypeFormData is used to handle form data.
	SerializationTypeFormData          = 131
	maxIndexForSerializationFastAccess = 255
)

var (
	primarySerializers  [maxIndexForSerializationFastAccess + 1]Serializer
	fallbackSerializers = make(map[int]Serializer)
)

// RegisterSerializer registers serializer, will be called by init function
// in third package.
func RegisterSerializer(serializationType int, s Serializer) {
	if serializationType >= 0 && serializationType <= maxIndexForSerializationFastAccess {
		primarySerializers[serializationType] = s
		return
	}
	fallbackSerializers[serializationType] = s
}

// MustRegisterSerializer registers serializer, will panic if the serializer
// has been registered.
//
// In most cases, the framework uses the init + RegisterSerializer method for registration. However, due to
// the unpredictable execution order of init functions, some unknown situations may arise. For example:
//
// If your code uses init + MustRegisterSerializer to forcibly register a component 'xxx', while the framework
// uses init + RegisterSerializer to register another component 'yyy', conflicts may occur. If the init function
// for MustRegisterSerializer is executed before the conflicting init function, MustRegisterSerializer might not raise
// an error or panic as expected.
//
// Therefore, it's important to be cautious when using MustRegisterSerializer and to carefully consider any
// potential conflicts or unintended consequences that may arise from its use.
func MustRegisterSerializer(serializationType int, s Serializer) {
	if GetSerializer(serializationType) != nil {
		panic("serializer already registered for type: " + cast.ToString(serializationType))
	}
	RegisterSerializer(serializationType, s)
}

// GetSerializer returns the serializer defined by serialization code.
func GetSerializer(serializationType int) Serializer {
	if serializationType >= 0 && serializationType <= maxIndexForSerializationFastAccess {
		return primarySerializers[serializationType]
	}
	return fallbackSerializers[serializationType]
}

// Unmarshal deserializes the in bytes into body. The specific serialization
// mode is defined by serializationType code, protobuf is default mode.
func Unmarshal(serializationType int, in []byte, body interface{}) error {
	if body == nil {
		return nil
	}
	if len(in) == 0 {
		return nil
	}
	if serializationType == SerializationTypeUnsupported {
		return nil
	}

	s := GetSerializer(serializationType)
	if s == nil {
		return errors.New("serializer not registered")
	}
	return s.Unmarshal(in, body)
}

// Marshal returns the serialized bytes from body. The specific serialization
// mode is defined by serializationType code, protobuf is default mode.
func Marshal(serializationType int, body interface{}) ([]byte, error) {
	if body == nil {
		return nil, nil
	}
	if serializationType == SerializationTypeUnsupported {
		return nil, nil
	}

	s := GetSerializer(serializationType)
	if s == nil {
		return nil, errors.New("serializer not registered")
	}
	return s.Marshal(body)
}

package codec

import (
	"fmt"

	"github.com/golang/protobuf/proto"
)

func init() {
	RegisterSerializer(SerializationTypePB, &PBSerialization{})
}

// PBSerialization provides protobuf serialization mode.
type PBSerialization struct{}

// Unmarshal deserializes the in bytes into body.
func (s *PBSerialization) Unmarshal(in []byte, body interface{}) error {
	msg, ok := body.(proto.Message)
	if !ok {
		return fmt.Errorf("failed to unmarshal body: expected proto.Message, got %T", body)
	}
	return proto.Unmarshal(in, msg)
}

// Marshal returns the serialized bytes in protobuf protocol.
func (s *PBSerialization) Marshal(body interface{}) ([]byte, error) {
	msg, ok := body.(proto.Message)
	if !ok {
		return nil, fmt.Errorf("failed to marshal body: expected proto.Message, got %T", body)
	}
	return proto.Marshal(msg)
}

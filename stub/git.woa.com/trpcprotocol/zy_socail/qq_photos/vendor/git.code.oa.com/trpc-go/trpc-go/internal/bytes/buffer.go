// Package bytes extends std/bytes to provide versatile utilities for buffers.
package bytes

import (
	"bytes"
	"sync"
)

var nopCloserBufferPool sync.Pool

func init() {
	nopCloserBufferPool = sync.Pool{
		New: func() interface{} {
			return &NopCloserBuffer{}
		},
	}
}

// NopCloserBuffer implements io.Closer, but the implementation is nop.
type NopCloserBuffer struct {
	bytes.Buffer
}

// Close implements io.Closer, it does nothing.
func (*NopCloserBuffer) Close() error {
	return nil
}

// GetNopCloserBuffer gets a NopCloserBuffer from pool.
func GetNopCloserBuffer() *NopCloserBuffer {
	return nopCloserBufferPool.Get().(*NopCloserBuffer)
}

// PutNopCloserBuffer puts a NopCloserBuffer to pool.
func PutNopCloserBuffer(b *NopCloserBuffer) {
	b.<PERSON><PERSON>()
	nopCloserBufferPool.Put(b)
}

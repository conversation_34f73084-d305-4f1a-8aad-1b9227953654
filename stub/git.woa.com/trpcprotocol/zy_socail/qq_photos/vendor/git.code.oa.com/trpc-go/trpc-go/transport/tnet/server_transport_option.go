//go:build linux || freebsd || dragonfly || darwin
// +build linux freebsd dragonfly darwin

package tnet

import (
	"runtime"
	"time"

	"git.woa.com/trpc-go/tnet"
)

// SetNumPollers sets the number of tnet pollers. Generally it is not actively used.
func SetNumPollers(n int) error {
	return tnet.SetNumPollers(n)
}

// ServerTransportOption is server transport option.
type ServerTransportOption func(o *ServerTransportOptions)

// ServerTransportOptions is server transport options struct.
type ServerTransportOptions struct {
	KeepAlivePeriod           time.Duration
	ReusePort                 bool
	MaxUDPPacketSize          int
	ExactUDPBufferSizeEnabled bool
}

// WithKeepAlivePeriod sets the TCP keep alive interval.
func WithKeepAlivePeriod(d time.Duration) ServerTransportOption {
	return func(opts *ServerTransportOptions) {
		opts.KeepAlivePeriod = d
	}
}

// WithReusePort returns a ServerTransportOption which enables reuse port or not.
func WithReusePort(reuse bool) ServerTransportOption {
	return func(opts *ServerTransportOptions) {
		opts.ReusePort = reuse
		if runtime.GOOS == "windows" {
			opts.ReusePort = false
		}
	}
}

// WithMaxUDPPacketSize sets the max UDP packet size.
func WithMaxUDPPacketSize(m int) ServerTransportOption {
	return func(opts *ServerTransportOptions) {
		opts.MaxUDPPacketSize = m
	}
}

// WithServerExactUDPBufferSizeEnabled sets whether to allocate an exact-sized buffer for UDP packets, false in default.
// If set to true, an exact-sized buffer is allocated for each UDP packet, requiring two system calls.
// If set to false, a fixed buffer size of maxUDPPacketSize is used, 65536 in default, requiring only one system call.
func WithServerExactUDPBufferSizeEnabled(enable bool) ServerTransportOption {
	return func(opts *ServerTransportOptions) {
		opts.ExactUDPBufferSizeEnabled = enable
	}
}

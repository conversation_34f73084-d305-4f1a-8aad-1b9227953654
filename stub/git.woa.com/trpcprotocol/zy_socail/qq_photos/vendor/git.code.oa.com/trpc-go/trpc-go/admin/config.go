package admin

import (
	"time"
)

const (
	defaultListenAddr   = "127.0.0.1:9028" // default listen port.
	defaultUseTLS       = false            // default doesn't use https.
	defaultReadTimeout  = time.Second * 3
	defaultWriteTimeout = time.Second * 60
	defaultSkipServe    = false
)

func loadDefaultConfig() *adminConfig {
	return &adminConfig{
		skipServe:    defaultSkipServe,
		addr:         defaultListenAddr,
		enableTLS:    defaultUseTLS,
		readTimeout:  defaultReadTimeout,
		writeTimeout: defaultWriteTimeout,
	}
}

// adminConfig manages trpc service configuration.
type adminConfig struct {
	addr         string
	enableTLS    bool
	readTimeout  time.Duration
	writeTimeout time.Duration
	version      string
	configPath   string
	skipServe    bool
}

func (a *adminConfig) getAddr() string {
	return a.addr
}

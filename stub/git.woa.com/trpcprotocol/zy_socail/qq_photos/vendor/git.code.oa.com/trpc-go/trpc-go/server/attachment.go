package server

import (
	"io"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/internal/attachment"
)

// Attachment stores the attachment of tRPC requests/responses.
type Attachment struct {
	attachment *attachment.Attachment
}

// Request returns Request Attachment.
func (a *Attachment) Request() io.Reader {
	return a.attachment.Request
}

// SetResponse sets Response attachment.
// If the response additionally implements the Sizer interface, it can significantly reduce memory copying for large
// attachments and reduce transmission time. Typically, you can pass bytes.NewReader, which already implements Sizer.
//
//	type Sizer interface {
//		Size() int64
//	}
func (a *Attachment) SetResponse(response io.Reader) {
	a.attachment.Response = response
}

// GetAttachment returns Attachment from msg.
// If there is no Attachment in the msg, an empty attachment bound to the msg will be returned.
func GetAttachment(msg codec.Msg) *Attachment {
	cm := msg.CommonMeta()
	if cm == nil {
		cm = make(codec.CommonMeta)
		msg.WithCommonMeta(cm)
	}
	a := cm[attachment.ServerAttachmentKey{}]
	if a == nil {
		a = &attachment.Attachment{Request: attachment.NoopAttachment{}, Response: attachment.NoopAttachment{}}
		cm[attachment.ServerAttachmentKey{}] = a
	}

	return &Attachment{attachment: a.(*attachment.Attachment)}
}

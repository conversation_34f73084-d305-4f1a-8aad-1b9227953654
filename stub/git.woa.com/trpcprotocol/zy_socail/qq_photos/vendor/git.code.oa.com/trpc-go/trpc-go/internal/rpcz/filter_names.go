// Package rpcz provides internal utilities for rpcz.
package rpcz

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/rpcz"
)

// FilterNames retrieves filter names from context.
func FilterNames(ctx context.Context) ([]string, bool) {
	value, ok := rpcz.SpanFromContext(ctx).Attribute(rpcz.TRPCAttributeFilterNames)
	if !ok {
		return nil, false
	}
	names, ok := value.([]string)
	return names, ok
}

// FilterName returns the name at the given index.
// Return "unknown" for invalid index.
func FilterName(names []string, index int) string {
	if index >= len(names) || index < 0 {
		const unknownName = "unknown"
		return unknownName
	}
	return names[index]
}

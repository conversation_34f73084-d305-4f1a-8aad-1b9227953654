package restful

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/internal/httprule"
)

// Pattern makes *httprule.PathTemplate accessible.
type Pattern struct {
	*httprule.PathTemplate
	// rawURLPath is the raw url path which defined in the proto file.
	rawURLPath string
}

// RawURLPath returns the raw url path which defined in the proto file.
func (p *Pattern) RawURLPath() string {
	return p.rawURLPath
}

// Parse parses the url path into a *Pattern. It should only be used by trpc-go-cmdline.
func Parse(urlPath string) (*Pattern, error) {
	tpl, err := httprule.Parse(urlPath)
	if err != nil {
		return nil, err
	}
	return &Pattern{PathTemplate: tpl, rawURLPath: urlPath}, nil
}

// Enforce ensures the url path is legal (will panic if illegal) and parses it into a *Pattern.
func Enforce(urlPath string) *Pattern {
	pattern, err := Parse(urlPath)
	if err != nil {
		panic(err)
	}
	return pattern
}

// restfulPatternKey is the key used to store the Pattern in context.
type restfulPatternKey struct{}

// newContextWithPattern creates a new context with the Pattern.
func newContextWithPattern(ctx context.Context, pattern *Pattern) context.Context {
	return context.WithValue(ctx, restfulPatternKey{}, pattern)
}

// PatternFromContext returns the Pattern from the context.
func PatternFromContext(ctx context.Context) (*Pattern, bool) {
	p, ok := ctx.Value(restfulPatternKey{}).(*Pattern)
	return p, ok
}

package frame

import "encoding/binary"

const (
	trpcFrameHeadLen = 16
	// fix import cycle trpc package
	// TrpcMagic_TRPC_MAGIC_VALUE
	// TrpcDataFrameType_TRPC_STREAM_FRAME
	// TrpcStreamFrameType_TRPC_STREAM_FRAME_INIT
	// TrpcStreamFrameType_TRPC_STREAM_FRAME_CLOSE
	trpcMagicVALUE            = 2352
	trpcStreamFrameType       = 1
	StreamFrameInit     uint8 = 0x01
	StreamFrameClose    uint8 = 0x04
)

// ParseTRPCStreamHeader parse the provided byte slice to stream header.
// trpc streaming protocol https://iwiki.woa.com/p/145446228
func ParseTRPCStreamHeader(bts []byte) (streamFrameType uint8, streamID uint32, ok bool) {
	if len(bts) < trpcFrameHeadLen {
		return 0, 0, false
	}
	magic := binary.BigEndian.Uint16(bts[:2])
	if magic != uint16(trpcMagicVALUE) {
		return 0, 0, false
	}
	if bts[2] != trpcStreamFrameType {
		return 0, 0, false
	}
	return bts[3], binary.BigEndian.Uint32(bts[:10]), true
}

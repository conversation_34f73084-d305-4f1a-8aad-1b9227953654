package config

// WithCodec returns an option which sets the codec by name.
func WithCodec(name string) LoadOption {
	return func(c *TrpcConfig) {
		c.decoder = GetCodec(name)
	}
}

// WithProvider returns an option which sets the provider by name.
func WithProvider(name string) LoadOption {
	return func(c *TrpcConfig) {
		c.p = GetProvider(name)
	}
}

// WithExpandEnv replaces ${var} in raw bytes with environment value of var.
// Note, method TrpcConfig.Bytes will return the replaced bytes.
func WithExpandEnv() LoadOption {
	return func(c *TrpcConfig) {
		c.expandEnv = true
	}
}

// WithWatch returns an option to start watch model
func WithWatch() LoadOption {
	return func(c *TrpcConfig) {
		c.watch = true
	}
}

// WithWatchHook returns an option to set log func for config change logger
func WithWatchHook(f func(WatchMessage)) LoadOption {
	return func(c *TrpcConfig) {
		c.watchHook = func(message WatchMessage) error {
			f(message)
			return nil
		}
	}
}

// WithWatchHookWithError returns an option to set a watch hook that explicitly returns an error.
// Typically, it is used in conjunction with implementing the DataProviderWithError interface.
func WithWatchHookWithError(f func(WatchMessage) error) LoadOption {
	return func(c *TrpcConfig) {
		c.watchHook = f
	}
}

// options is config option.
type options struct{}

// Option is the option for config provider sdk.
type Option func(*options)

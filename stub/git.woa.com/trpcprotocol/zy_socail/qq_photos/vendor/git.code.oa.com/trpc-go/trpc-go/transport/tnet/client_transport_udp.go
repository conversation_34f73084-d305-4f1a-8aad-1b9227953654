//go:build linux || freebsd || dragonfly || darwin
// +build linux freebsd dragonfly darwin

package tnet

import (
	"bytes"
	"context"
	"fmt"
	"net"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	inet "git.code.oa.com/trpc-go/trpc-go/internal/net"
	"git.code.oa.com/trpc-go/trpc-go/internal/report"
	"git.code.oa.com/trpc-go/trpc-go/internal/rpczenable"
	"git.code.oa.com/trpc-go/trpc-go/rpcz"
	"git.code.oa.com/trpc-go/trpc-go/transport"
	"git.code.oa.com/trpc-go/trpc-go/transport/internal/dialer"
	ierrs "git.code.oa.com/trpc-go/trpc-go/transport/internal/errs"
	"git.code.oa.com/trpc-go/trpc-go/transport/internal/pool"
	"git.woa.com/trpc-go/tnet"
)

// defaultUDPPool is the global UDP connection pool instance.
var defaultUDPPool *pool.UDPPool

func init() {
	// Initialize the global UDP connection pool with default configuration.
	var err error
	defaultConfig := pool.DefaultConfig()
	defaultConfig.ConnectionMode = dialer.Connected // Set the connection mode
	if defaultUDPPool, err = pool.NewPool(defaultConfig); err != nil {
		// Log error but do not panic since UDP connection pool is optional.
		fmt.Printf("Failed to create default UDP connection pool: %v.\n", err)
	}
}

func (c *clientTransport) udpRoundTrip(ctx context.Context, reqData []byte,
	opts *transport.RoundTripOptions) ([]byte, error) {
	// If connection pool is disabled or not initialized, use direct dial.
	if opts.DisableConnectionPool || defaultUDPPool == nil || opts.ConnectionMode == transport.NotConnected {
		return c.udpRoundTripDirect(ctx, reqData, opts)
	}

	// Get connection from pool.
	conn, err := defaultUDPPool.Get(ctx, dialer.DialOptions{
		Network:                   opts.Network,
		Address:                   opts.Address,
		LocalAddr:                 opts.LocalAddr,
		DialUDP:                   dialUDP,
		DialTimeout:               opts.DialTimeout,
		ConnectionMode:            opts.ConnectionMode,
		ExactUDPBufferSizeEnabled: c.opts.ExactUDPBufferSizeEnabled,
	})
	if err != nil {
		return nil, err
	}
	defer conn.Close() // This will return connection to pool instead of closing.

	msg := codec.Message(ctx)
	msg.WithRemoteAddr(inet.ResolveAddress(opts.Network, opts.Address))
	msg.WithLocalAddr(conn.LocalAddr())

	// Send a request.
	report.UDPClientTransportSendSize.Set(float64(len(reqData)))
	type getRawConn interface {
		GetRawConn() net.PacketConn
	}
	grc, ok := conn.(getRawConn)
	if !ok {
		return nil, errs.NewFrameError(errs.RetClientNetErr, "udp client transport: getRawConn failed")
	}
	rc := grc.GetRawConn()
	trc, ok := rc.(tnet.PacketConn)
	if !ok {
		return nil, errs.NewFrameError(errs.RetClientNetErr, "udp client transport: getRawConn failed")
	}
	if err := udpWriteFrame(ctx, trc, reqData, opts); err != nil {
		return nil, err
	}
	// Receive a response.
	rsp, err := udpReadFrame(ctx, trc, opts)
	if err != nil {
		report.UDPClientTransportReadFail.Incr()
		return nil, err
	}
	report.UDPClientTransportReceiveSize.Set(float64(len(rsp)))
	return rsp, nil
}

// udpRoundTripDirect handles UDP requests without connection pool.
func (c *clientTransport) udpRoundTripDirect(ctx context.Context, reqData []byte,
	opts *transport.RoundTripOptions) ([]byte, error) {
	ln, raddr, err := dialer.DialUDP(ctx, dialer.DialOptions{
		Network:                   opts.Network,
		Address:                   opts.Address,
		LocalAddr:                 opts.LocalAddr,
		DialUDP:                   dialUDP,
		DialTimeout:               opts.DialTimeout,
		ConnectionMode:            opts.ConnectionMode,
		ExactUDPBufferSizeEnabled: c.opts.ExactUDPBufferSizeEnabled,
	})
	if err != nil {
		return nil, err
	}
	defer ln.Close()
	conn, ok := ln.(tnet.PacketConn)
	if !ok {
		return nil, errs.NewFrameError(errs.RetClientConnectFail,
			"tnet udp client transport: conn is not a tnet.PacketConn")
	}

	msg := codec.Message(ctx)
	msg.WithRemoteAddr(raddr)
	msg.WithLocalAddr(conn.LocalAddr())
	// Send a request.
	report.UDPClientTransportSendSize.Set(float64(len(reqData)))
	if err := udpWriteFrame(ctx, conn, reqData, opts); err != nil {
		return nil, err
	}
	// Receive a response.
	rsp, err := udpReadFrame(ctx, conn, opts)
	if err != nil {
		report.UDPClientTransportReadFail.Incr()
		return nil, err
	}
	report.UDPClientTransportReceiveSize.Set(float64(len(rsp)))
	return rsp, nil
}

func udpWriteFrame(ctx context.Context, conn tnet.PacketConn, reqData []byte, opts *transport.RoundTripOptions) error {
	if rpczenable.Enabled {
		span := rpcz.SpanFromContext(ctx)
		_, ender := span.NewChild("SendMessage")
		defer ender.End()
	}

	// Sending udp request packets
	var num int
	var err error
	if opts.ConnectionMode == transport.Connected {
		num, err = conn.Write(reqData)
	} else {
		num, err = conn.WriteTo(reqData, codec.Message(ctx).RemoteAddr())
	}
	if err != nil {
		return ierrs.WrapAsClientTimeoutErrOr(err, errs.RetClientNetErr, "tnet udp client transport WriteTo failed")
	}
	if num != len(reqData) {
		return errs.NewFrameError(errs.RetClientNetErr, "tnet udp client transport WriteTo: num mismatch")
	}
	return nil
}

func udpReadFrame(ctx context.Context, conn tnet.PacketConn, opts *transport.RoundTripOptions) ([]byte, error) {
	if rpczenable.Enabled {
		span := rpcz.SpanFromContext(ctx)
		_, ender := span.NewChild("ReceiveMessage")
		defer ender.End()
	}
	// If it is SendOnly, returns directly without waiting for the server's response.
	if opts.ReqType == transport.SendOnly {
		return nil, errs.ErrClientNoResponse
	}

	// Receive server's response.
	packet, _, err := conn.ReadPacket()
	if err != nil {
		return nil, ierrs.WrapAsClientTimeoutErrOr(err, errs.RetClientNetErr,
			"tnet udp client transport ReadPacket failed")
	}
	defer packet.Free()
	rawData, err := packet.Data()
	if err != nil {
		return nil, errs.NewFrameError(errs.RetClientNetErr, "tnet udp client transport read packet data: "+err.Error())
	}
	buf := bytes.NewBuffer(rawData)
	framer := opts.FramerBuilder.New(buf)
	rsp, err := framer.ReadFrame()
	if err != nil {
		return nil, errs.NewFrameError(errs.RetClientReadFrameErr, "tnet udp client transport ReadFrame: "+err.Error())
	}
	return rsp, nil
}

func dialUDP(ctx context.Context, opts dialer.DialOptions) (net.PacketConn, error) {
	if opts.ConnectionMode == transport.NotConnected {
		// Listen on all available IP addresses of the local system by default,
		// and a port number is automatically chosen.
		const defaultLocalAddr = ":"
		localAddr := defaultLocalAddr
		if opts.LocalAddr != "" {
			localAddr = opts.LocalAddr
		}
		lns, err := tnet.ListenPackets(opts.Network, localAddr, false)
		if err != nil {
			return nil, errs.NewFrameError(errs.RetClientNetErr,
				"tnet udp client transport listen packets: "+err.Error())
		}
		svr, err := tnet.NewUDPService(
			lns,
			func(conn tnet.PacketConn) error { return nil },
			tnet.WithExactUDPBufferSizeEnabled(opts.ExactUDPBufferSizeEnabled))
		if err != nil {
			return nil, errs.NewFrameError(errs.RetClientNetErr, "tnet udp client transport new service: "+err.Error())
		}
		go svr.Serve(ctx)
		return lns[0], nil
	}
	conn, err := tnet.DialUDP(opts.Network, opts.Address, opts.DialTimeout)
	if err != nil {
		return nil, errs.NewFrameError(errs.RetClientConnectFail,
			fmt.Sprintf("tnet udp client transport dial udp: %s", err.Error()))
	}
	conn.SetExactUDPBufferSizeEnabled(opts.ExactUDPBufferSizeEnabled)
	return conn, nil
}

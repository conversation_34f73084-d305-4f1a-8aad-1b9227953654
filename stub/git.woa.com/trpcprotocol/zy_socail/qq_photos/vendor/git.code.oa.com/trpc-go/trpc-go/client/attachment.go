package client

import (
	"io"

	"git.code.oa.com/trpc-go/trpc-go/internal/attachment"
)

// Attachment stores the Attachment of tRPC requests/responses.
type Attachment struct {
	attachment attachment.Attachment
}

// NewAttachment returns a new Attachment whose response Attachment is a NoopAttachment.
// If the request additionally implements the Sizer interface, it can significantly reduce memory copying for large
// attachments and reduce transmission time. Typically, you can pass bytes.NewReader, which already implements Sizer.
//
//	type Sizer interface {
//		Size() int64
//	}
func NewAttachment(request io.Reader) *Attachment {
	return &Attachment{attachment: attachment.Attachment{Request: request, Response: attachment.NoopAttachment{}}}
}

// Response returns Response Attachment.
func (a *Attachment) Response() io.Reader {
	return a.attachment.Response
}

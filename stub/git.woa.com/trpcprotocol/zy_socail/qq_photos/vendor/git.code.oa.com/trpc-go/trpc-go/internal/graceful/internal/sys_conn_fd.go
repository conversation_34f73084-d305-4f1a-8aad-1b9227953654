//go:build !windows

package graceful

import (
	"fmt"
	"syscall"
)

func sysConnFd[T any](l T) (int, error) {
	l = Unwrap(l)
	sysConn, ok := (interface{})(l).(syscall.Conn)
	if !ok {
		return 0, fmt.<PERSON><PERSON><PERSON>("%T is not a syscall.Conn", l)
	}
	rawConn, err := sysConn.SyscallConn()
	if err != nil {
		return 0, fmt.Erro<PERSON>("failed to get rawConn: %w", err)
	}
	var fd int
	if err := rawConn.Control(func(fileDescriptor uintptr) {
		fd = int(fileDescriptor)
	}); err != nil {
		return 0, fmt.E<PERSON>rf("failed to call Control: %w", err)
	}
	return fd, nil
}

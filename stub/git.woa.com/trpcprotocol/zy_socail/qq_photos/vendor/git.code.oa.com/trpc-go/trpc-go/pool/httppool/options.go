package httppool

import (
	"time"

	"git.code.oa.com/trpc-go/trpc-go/internal/http/config"
)

// NewOptions creates a new Options with default values.
func NewOptions() Options {
	return Options{
		MaxIdleConns:        config.DefaultMaxIdleConns,
		MaxIdleConnsPerHost: config.DefaultMaxIdleConnsPerHost,
		MaxConnsPerHost:     config.DefaultMaxConnsPerHost,
		IdleConnTimeout:     config.DefaultIdleConnTimeout,
	}
}

// Options indicates pool configuration.
type Options struct {
	// MaxIdleConns controls the maximum number of idle connections across all hosts, default 0, which means no limit.
	MaxIdleConns int
	// MaxIdleConnsPerHost controls the maximum idle connections to keep per-host, default 100.
	MaxIdleConnsPerHost int
	// MaxConnsPerHost optionally limits the total number of connections per host, default 0, which means no limit.
	MaxConnsPerHost int
	// IdleConnTimeout is the maximum amount of time an idle connection will remain idle before closing,
	// default 50s.
	IdleConnTimeout time.Duration
}

// Option is the Options helper.
type Option func(*Options)

// WithMaxIdleConns returns an Option which sets the maximum number of idle connections across all hosts,
// default 0, which means no limit.
func WithMaxIdleConns(m int) Option {
	return func(o *Options) {
		o.MaxIdleConns = m
	}
}

// WithMaxIdleConnsPerHost returns an Option which sets the maximum idle connections to keep per-host, default 100.
func WithMaxIdleConnsPerHost(m int) Option {
	return func(o *Options) {
		o.MaxIdleConnsPerHost = m
	}
}

// WithMaxConnsPerHost returns an Option which sets the total number of connections per host,
// default 0, which means no limit.
func WithMaxConnsPerHost(m int) Option {
	return func(o *Options) {
		o.MaxConnsPerHost = m
	}
}

// WithIdleConnTimeout returns an Option which sets the maximum amount of time an idle connection
// will remain idle before closing, default 50s.
func WithIdleConnTimeout(t time.Duration) Option {
	return func(o *Options) {
		o.IdleConnTimeout = t
	}
}

package http

import (
	"context"
	"errors"

	"git.code.oa.com/trpc-go/trpc-go/server"
	"github.com/valyala/fasthttp"
)

// CtxKey is used to store context.Context in requestCtx.
type CtxKey struct{}

// GetContext gets context.Context from requestCtx.
func GetContext(requestCtx *fasthttp.RequestCtx) (context.Context, bool) {
	ctx, ok := requestCtx.UserValue(CtxKey{}).(context.Context)
	return ctx, ok
}

// FastHTTPHandleFunc registers fasthttp handler with custom route.
// If handler need ctx (context.Context), users can get by requestCtx.UserValue(CtxKey{})
func FastHTTPHandleFunc(pattern string, handler func(requestCtx *fasthttp.RequestCtx)) {
	ServiceDesc.Methods = append(ServiceDesc.Methods, generateMethodFastHTTP(pattern, handler))
}

// generateMethod generates server method.
func generateMethodFastHTTP(pattern string, handler fasthttp.RequestHandler) server.Method {
	handlerFunc := func(_ interface{}, ctx context.Context, f server.FilterFunc) (rspBody interface{}, err error) {
		filters, err := f(nil)
		if err != nil {
			return nil, err
		}
		handleFunc := func(ctx context.Context, _ interface{}) (rspBody interface{}, err error) {
			requestCtx := RequestCtx(ctx)
			if requestCtx == nil {
				return nil, errors.New("fasthttp Handle missing requestCtx in context")
			}
			// Store context.Context.
			requestCtx.SetUserValue(CtxKey{}, ctx)
			// Handle error in handler.
			// fasthttp.RequestHandler will NOT return err.
			handler(requestCtx)
			return nil, nil
		}
		return filters.Filter(ctx, nil, handleFunc)
	}
	return server.Method{
		Name: pattern,
		Func: handlerFunc,
	}
}

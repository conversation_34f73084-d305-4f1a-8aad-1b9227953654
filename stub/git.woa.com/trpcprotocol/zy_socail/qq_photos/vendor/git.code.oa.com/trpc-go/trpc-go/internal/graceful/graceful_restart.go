//go:build !windows

package graceful

import (
	"net"

	igr "git.code.oa.com/trpc-go/trpc-go/internal/graceful/internal"
)

// Restart attempts to perform a graceful restart.
var Restart = igr.Restart

// Listen creates a net.Listener on network address and supports port reuse.
var Listen = igr.Listen

// ListenPacket creates a net.PacketConn on network address and supports port reuse.
var ListenPacket = igr.ListenPacket

var UnwrapListener = igr.Unwrap[net.Listener]

var UnwrapPacketConn = igr.Unwrap[net.PacketConn]

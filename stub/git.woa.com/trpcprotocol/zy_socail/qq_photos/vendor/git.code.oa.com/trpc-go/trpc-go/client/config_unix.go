//go:build linux || freebsd || dragonfly || darwin
// +build linux freebsd dragonfly darwin

package client

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/transport"
	tnettransport "git.code.oa.com/trpc-go/trpc-go/transport/tnet"
	tnetmultiplexed "git.code.oa.com/trpc-go/trpc-go/transport/tnet/multiplexed"
)

// tnetClientPoolOption return transport roundtrip option for tnet.
func (cfg *BackendConfig) tnetClientPoolOption() (transport.RoundTripOption, error) {
	switch *cfg.ConnType {
	case ConnTypeShort:
		return transport.WithDisableConnectionPool(), nil
	case ConnTypeConnPool:
		return cfg.tnetClientConnPoolOption(), nil
	case ConnTypeMultiplexedPool:
		return cfg.tnetClientMultiplexedPoolOption(), nil
	default:
		return nil,
			fmt.Errorf("transport %v doesn't support connection type %v; supported connection types are [%v, %v, %v]",
				cfg.Transport, *cfg.ConnType, ConnTypeShort, ConnTypeConnPool, ConnTypeMultiplexedPool)
	}
}

// tnetClientPoolOption return transport roundtrip option for tnet connection pool.
func (cfg *BackendConfig) tnetClientConnPoolOption() transport.RoundTripOption {
	// tnet connection pool options is the same as gonet.
	return transport.WithDialPool(tnettransport.NewConnectionPool(cfg.connpoolOptions()...))
}

// tnetClientPoolOption return transport roundtrip option for tnet multiplexed pool.
func (cfg *BackendConfig) tnetClientMultiplexedPoolOption() transport.RoundTripOption {
	var opts []tnetmultiplexed.OptPool
	if cfg.Multiplexed.MultiplexedDialTimeout != nil {
		opts = append(opts, tnetmultiplexed.WithDialTimeout(*cfg.Multiplexed.MultiplexedDialTimeout))
	}
	if cfg.Multiplexed.MaxVirConnsPerConn != nil {
		opts = append(opts, tnetmultiplexed.WithMaxConcurrentVirtualConnsPerConn(*cfg.Multiplexed.MaxVirConnsPerConn))
	}
	// Option enable_metrics is only used in tnet.
	if cfg.Multiplexed.EnableMetrics != nil && *cfg.Multiplexed.EnableMetrics {
		opts = append(opts, tnetmultiplexed.WithEnableMetrics())
	}
	if cfg.Multiplexed.ConnsPerHost != nil {
		opts = append(opts, tnetmultiplexed.WithConnectNumber(*cfg.Multiplexed.ConnsPerHost))
	}
	if cfg.Multiplexed.ConcurrentDialGroupsPerHost != nil {
		opts = append(opts, tnetmultiplexed.WithConcurrentDialGroupsPerHost(*cfg.Multiplexed.ConcurrentDialGroupsPerHost))
	}
	return transport.WithMultiplexedPool(tnettransport.NewMultiplexdPool(opts...))
}

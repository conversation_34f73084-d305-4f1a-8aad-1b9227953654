package transport

import (
	"context"
	"fmt"
	"net"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	inet "git.code.oa.com/trpc-go/trpc-go/internal/net"
	"git.code.oa.com/trpc-go/trpc-go/internal/packetbuffer"
	"git.code.oa.com/trpc-go/trpc-go/internal/report"
	"git.code.oa.com/trpc-go/trpc-go/pool/objectpool"
	"git.code.oa.com/trpc-go/trpc-go/transport/internal/dialer"
	ierrs "git.code.oa.com/trpc-go/trpc-go/transport/internal/errs"
	"git.code.oa.com/trpc-go/trpc-go/transport/internal/pool"
)

const defaultUDPRecvBufSize = 64 * 1024

var (
	// udpBufPool is a pool for UDP receive buffers to reduce memory allocations.
	udpBufPool = objectpool.NewBytesPool(defaultUDPRecvBufSize)

	// defaultUDPPool is the global UDP connection pool instance.
	defaultUDPPool *pool.UDPPool
)

func init() {
	// Initialize the global UDP connection pool with default configuration.
	var err error
	defaultConfig := pool.DefaultConfig()
	defaultConfig.ConnectionMode = dialer.Connected // Set the connection mode
	if defaultUDPPool, err = pool.NewPool(defaultConfig); err != nil {
		// Log error but do not panic since UDP connection pool is optional.
		fmt.Printf("Failed to create default UDP connection pool: %v.\n", err)
	}
}

// udpRoundTrip sends UDP requests.
func (c *clientTransport) udpRoundTrip(ctx context.Context, reqData []byte,
	opts *RoundTripOptions) ([]byte, error) {
	if opts.FramerBuilder == nil {
		return nil, errs.NewFrameError(errs.RetClientConnectFail,
			"udp client transport: framer builder empty")
	}

	// If connection pool is disabled or not initialized, use direct dial.
	if opts.DisableConnectionPool || defaultUDPPool == nil || opts.ConnectionMode == dialer.NotConnected {
		return c.udpRoundTripDirect(ctx, reqData, opts)
	}

	// Get connection from pool.
	conn, err := defaultUDPPool.Get(ctx, dialer.DialOptions{
		Network:        opts.Network,
		Address:        opts.Address,
		LocalAddr:      opts.LocalAddr,
		DialUDP:        dialer.DefaultDialUDP,
		DialTimeout:    opts.DialTimeout,
		ConnectionMode: opts.ConnectionMode,
	})
	if err != nil {
		return nil, err
	}
	defer conn.Close() // This will return connection to pool instead of closing.

	msg := codec.Message(ctx)
	msg.WithRemoteAddr(inet.ResolveAddress(opts.Network, opts.Address))
	msg.WithLocalAddr(conn.LocalAddr())

	if ctx.Err() == context.Canceled {
		return nil, errs.NewFrameError(errs.RetClientCanceled,
			"udp client transport canceled before Write: "+ctx.Err().Error())
	}
	if ctx.Err() == context.DeadlineExceeded {
		return nil, errs.NewFrameError(errs.RetClientTimeout,
			"udp client transport timeout before Write: "+ctx.Err().Error())
	}

	report.UDPClientTransportSendSize.Set(float64(len(reqData)))
	type getRawConn interface {
		GetRawConn() net.PacketConn
	}
	grc, ok := conn.(getRawConn)
	if !ok {
		return nil, errs.NewFrameError(errs.RetClientNetErr, "udp client transport: getRawConn failed")
	}
	rc := grc.GetRawConn()
	if err := c.udpWriteFrame(rc, reqData, nil, opts); err != nil {
		return nil, err
	}
	return c.udpReadFrame(ctx, rc, opts)
}

// udpRoundTripDirect handles UDP requests without connection pool.
func (c *clientTransport) udpRoundTripDirect(ctx context.Context, reqData []byte,
	opts *RoundTripOptions) ([]byte, error) {
	conn, addr, err := dialer.DialUDP(ctx, dialer.DialOptions{
		Network:        opts.Network,
		Address:        opts.Address,
		LocalAddr:      opts.LocalAddr,
		DialUDP:        dialer.DefaultDialUDP,
		DialTimeout:    opts.DialTimeout,
		ConnectionMode: opts.ConnectionMode,
	})
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	msg := codec.Message(ctx)
	msg.WithRemoteAddr(addr)
	msg.WithLocalAddr(conn.LocalAddr())

	if ctx.Err() == context.Canceled {
		return nil, errs.NewFrameError(errs.RetClientCanceled,
			"udp client transport canceled before Write: "+ctx.Err().Error())
	}
	if ctx.Err() == context.DeadlineExceeded {
		return nil, errs.NewFrameError(errs.RetClientTimeout,
			"udp client transport timeout before Write: "+ctx.Err().Error())
	}

	report.UDPClientTransportSendSize.Set(float64(len(reqData)))
	if err := c.udpWriteFrame(conn, reqData, addr, opts); err != nil {
		return nil, err
	}
	return c.udpReadFrame(ctx, conn, opts)
}

// udpReadFrame reads UDP frame.
func (c *clientTransport) udpReadFrame(
	ctx context.Context, conn net.PacketConn, opts *RoundTripOptions) ([]byte, error) {
	// If it is SendOnly, returns directly without waiting for the server's response.
	if opts.ReqType == SendOnly {
		return nil, errs.ErrClientNoResponse
	}

	select {
	case <-ctx.Done():
		return nil, errs.NewFrameError(errs.RetClientTimeout, "udp client transport select after Write: "+ctx.Err().Error())
	default:
	}

	recvData := udpBufPool.Get()
	defer udpBufPool.Put(recvData)
	buf := packetbuffer.New(recvData)
	fr := opts.FramerBuilder.New(buf)
	// Receive server's response.
	num, _, err := conn.ReadFrom(buf.Bytes())
	if err != nil {
		if e, ok := err.(net.Error); ok && e.Timeout() {
			return nil, errs.NewFrameError(errs.RetClientTimeout, "udp client transport ReadFrom: "+err.Error())
		}
		return nil, errs.NewFrameError(errs.RetClientNetErr, "udp client transport ReadFrom: "+err.Error())
	}
	if num == 0 {
		return nil, errs.NewFrameError(errs.RetClientNetErr, "udp client transport ReadFrom: num empty")
	}
	// Update the buffer according to the actual length of the received data.
	buf.Advance(num)
	req, err := fr.ReadFrame()
	if err != nil {
		report.UDPClientTransportReadFail.Incr()
		return nil, errs.NewFrameError(errs.RetClientReadFrameErr,
			"udp client transport ReadFrame: "+err.Error())
	}
	// One packet of udp corresponds to one trpc packet,
	// and after parsing, there should not be any remaining data
	if buf.UnRead() > 0 {
		report.UDPClientTransportUnRead.Incr()
		return nil, errs.NewFrameError(errs.RetClientReadFrameErr,
			fmt.Sprintf("udp client transport ReadFrame: remaining %d bytes data", buf.UnRead()))
	}
	report.UDPClientTransportReceiveSize.Set(float64(len(req)))
	// Framer is used for every request so there is no need to copy memory.
	return req, nil
}

// udpWriteFrame writes UDP frame.
func (c *clientTransport) udpWriteFrame(conn net.PacketConn,
	reqData []byte, addr *net.UDPAddr, opts *RoundTripOptions) error {
	// Sending udp request packets
	var num int
	var err error
	if opts.ConnectionMode == Connected {
		udpconn := conn.(*net.UDPConn)
		num, err = udpconn.Write(reqData)
	} else {
		num, err = conn.WriteTo(reqData, addr)
	}
	if err != nil {
		return ierrs.WrapAsClientTimeoutErrOr(err, errs.RetClientNetErr, "udp client transport WriteTo failed")
	}
	if num != len(reqData) {
		return errs.NewFrameError(errs.RetClientNetErr, "udp client transport WriteTo: num mismatch")
	}
	return nil
}

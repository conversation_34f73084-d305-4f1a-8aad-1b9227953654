package rpcz

import (
	"math/rand"

	"git.code.oa.com/trpc-go/trpc-go/internal/random"
)

// randomIDGenerator generates random span ID.
type randomIDGenerator struct {
	r *rand.Rand
}

// newSpanID returns a non-negative span ID randomly.
func (gen *randomIDGenerator) newSpanID() SpanID {
	return SpanID(gen.r.Int63())
}

func newRandomIDGenerator() *randomIDGenerator {
	return &randomIDGenerator{r: random.New()}
}

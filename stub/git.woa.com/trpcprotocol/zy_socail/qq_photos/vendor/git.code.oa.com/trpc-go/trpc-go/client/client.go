// Package client is tRPC-Go client side implementation,
// including network transportation, resolving, routing etc.
package client

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/hashicorp/go-multierror"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/internal/attachment"
	icodec "git.code.oa.com/trpc-go/trpc-go/internal/codec"
	inprocess "git.code.oa.com/trpc-go/trpc-go/internal/local/inprocess"
	inaming "git.code.oa.com/trpc-go/trpc-go/internal/naming"
	inet "git.code.oa.com/trpc-go/trpc-go/internal/net"
	"git.code.oa.com/trpc-go/trpc-go/internal/protocol"
	iprotocol "git.code.oa.com/trpc-go/trpc-go/internal/protocol"
	ireflect "git.code.oa.com/trpc-go/trpc-go/internal/reflect"
	"git.code.oa.com/trpc-go/trpc-go/internal/report"
	"git.code.oa.com/trpc-go/trpc-go/internal/rpczenable"
	"git.code.oa.com/trpc-go/trpc-go/internal/scope"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.code.oa.com/trpc-go/trpc-go/naming/selector"
	"git.code.oa.com/trpc-go/trpc-go/overloadctrl"
	"git.code.oa.com/trpc-go/trpc-go/rpcz"
	"git.code.oa.com/trpc-go/trpc-go/transport"
)

// Client is the interface that initiates RPCs and sends request messages to a server.
type Client interface {
	// Invoke performs a unary RPC.
	Invoke(ctx context.Context, reqBody interface{}, rspBody interface{}, opt ...Option) error
}

// BroadcastClient is the interface that initiates broadcast RPCs.
type BroadcastClient[RspType any] interface {
	BroadcastInvoke(
		ctx context.Context,
		reqBody interface{},
		opt ...Option,
	) (
		[]*BroadcastRsp[RspType],
		error,
	)
}

// BroadcastRsp is the generic broadcast response type.
type BroadcastRsp[RspType any] struct {
	Node *registry.Node
	Rsp  *RspType
	Err  error
}

// DefaultClient is the default global client.
// It's thread-safe.
var DefaultClient = New()

// New creates a client that uses default client transport.
var New = func() Client {
	return &client{}
}

// client is the default implementation of Client with
// pluggable codec, transport, filter etc.
type client struct{}

// broadcastClient is a concrete implementation of the BroadcastClient interface.
type broadcastClient[RspType any] struct {
	// cli is just a *client used to reuse the client's methods.
	cli *client
}

// NewBroadcastClient creates a new BroadcastClient.
func NewBroadcastClient[RspType any]() BroadcastClient[RspType] {
	return &broadcastClient[RspType]{cli: &client{}}
}

// Invoke invokes a backend call by passing in custom request/response message
// and running selector filter, codec, transport etc.
func (c *client) Invoke(ctx context.Context, reqBody interface{}, rspBody interface{}, opt ...Option) (err error) {
	// The generic message structure data of the current request is retrieved from the context,
	// and each backend call uses a new msg generated by the client stub code.
	ctx, msg := codec.EnsureMessage(ctx)

	// Get client options.
	opts, err := c.getOptions(msg, opt...)
	if err != nil {
		return err
	}
	return c.unaryInvoke(ctx, reqBody, rspBody, opts)
}

// unaryInvoke performs a single invocation.
func (c *client) unaryInvoke(ctx context.Context, reqBody interface{}, rspBody interface{}, opts *Options) (err error) {
	ctx, msg := codec.EnsureMessage(ctx)

	var (
		span  rpcz.Span
		ender rpcz.Ender
	)
	if rpczenable.Enabled {
		span, ender, ctx = rpcz.NewSpanContext(ctx, "client")
		defer func() {
			span.SetAttribute(rpcz.TRPCAttributeRPCName, msg.ClientRPCName())
			if err == nil {
				span.SetAttribute(rpcz.TRPCAttributeError, msg.ClientRspErr())
			} else {
				span.SetAttribute(rpcz.TRPCAttributeError, err)
			}
			ender.End()
		}()
	}

	// Update Msg by options.
	c.updateMsg(msg, opts)

	fullLinkDeadline, ok := ctx.Deadline()
	if ok {
		timeout := fullLinkDeadline.Sub(time.Now())
		if timeout <= 0 {
			return errs.NewFrameError(errs.RetClientFullLinkTimeout, "fullLinkDeadline has already passed")
		}
	}
	if opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}
	if deadline, ok := ctx.Deadline(); ok {
		timeout := deadline.Sub(time.Now())
		if timeout <= 0 {
			return errs.NewFrameError(errs.RetClientTimeout, "")
		}
		msg.WithRequestTimeout(timeout)
	}
	if ok && (opts.Timeout <= 0 || time.Until(fullLinkDeadline) < opts.Timeout) {
		opts.fixTimeout = mayConvert2FullLinkTimeout
	}

	// Start filter chain processing.
	filters := c.fixFilters(opts)
	if rpczenable.Enabled {
		span.SetAttribute(rpcz.TRPCAttributeFilterNames, opts.FilterNames)
	}
	return filters.Filter(contextWithOptions(ctx, opts), reqBody, rspBody, callFunc)
}

// BroadcastInvoke is the generic broadcast invoke function.
func (bc *broadcastClient[RspType]) BroadcastInvoke(
	ctx context.Context,
	reqBody interface{},
	opt ...Option,
) (
	rsps []*BroadcastRsp[RspType],
	err error,
) {
	ctx, msg := codec.EnsureMessage(ctx)

	// Get client options.
	opts, err := bc.cli.getOptions(msg, opt...)
	if err != nil {
		return nil, err
	}

	// Execute a pseudo call to get the node list.
	var rspBody RspType
	nodeList, err := getNodeList(ctx, reqBody, rspBody, opts)
	if err != nil {
		return nil, fmt.Errorf("fail to get node list: %w", err)
	}

	var mg multierror.Group
	rsps = make([]*BroadcastRsp[RspType], len(nodeList))

	for i, node := range nodeList {
		i, node := i, node
		ctxBrc, msgBrc := codec.WithCloneMessage(ctx)
		optsBrc := opts.clone()
		optsBrc.rebuildSliceCapacity()

		mg.Go(func() error {
			defer codec.PutBackMessage(msgBrc)
			optsBrc.Target = fmt.Sprintf("ip://%s", node.Address)
			if err := optsBrc.parseTarget(); err != nil {
				return err
			}

			optsBrc.SelectOptions = append(optsBrc.SelectOptions, selector.WithBroadcast(false))
			var rspBrc RspType
			err := bc.cli.unaryInvoke(ctxBrc, reqBody, &rspBrc, optsBrc)

			rsps[i] = &BroadcastRsp[RspType]{
				Node: node,
				Rsp:  &rspBrc,
				Err:  err,
			}

			if err != nil {
				return fmt.Errorf("fial to call %s: %w", optsBrc.Target, err)
			}
			return nil
		})
	}
	if err := mg.Wait(); err != nil {
		return rsps, fmt.Errorf("fail to broadcast: %w", err)
	}
	return rsps, nil
}

// get broadcast node list use a pseudo invoke.
func getNodeList(
	ctx context.Context,
	reqBody interface{},
	rspBody interface{},
	opts *Options,
) ([]*registry.Node, error) {
	// Execute a pseudo call to get the node list.
	pseudoOpts := opts.clone()
	pseudoOpts.rebuildSliceCapacity()
	pseudoOpts.SelectOptions = append(pseudoOpts.SelectOptions, selector.WithBroadcast(true))

	// Get the specific node being called.
	node := &registry.Node{}
	pseudoOpts.Node = &onceNode{Node: node}
	pseudoOpts.Filters = filter.ClientChain{selectorFilter, pseudoFilter}

	// ctx needs a deep copy.
	pseudoCtx, pseudoMsg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(pseudoMsg)
	cli, ok := DefaultClient.(*client)
	if !ok {
		return nil, errs.Newf(errs.RetClientRouteErr, "DefaultClient type assertion failed: expected *client, got %T.", DefaultClient)
	}
	if err := cli.unaryInvoke(pseudoCtx, reqBody, rspBody, pseudoOpts); err != nil {
		return nil, err
	}

	nodeListData, ok := node.Metadata[inaming.BroadcastNodeListKey]
	if !ok {
		return nil, errs.Newf(
			errs.RetClientRouteErr,
			"metadata not found: %s, current selector version may not support broadcast calls",
			inaming.BroadcastNodeListKey,
		)
	}

	nodeList, ok := nodeListData.([]*registry.Node)
	if !ok {
		return nil, errs.New(errs.RetClientRouteErr, "node list parsing error")
	}

	if len(nodeList) == 0 {
		return nil, errs.New(errs.RetClientRouteErr, "node list is empty")
	}

	return nodeList, nil
}

// getOptions returns Options needed by each RPC.
func (c *client) getOptions(msg codec.Msg, opt ...Option) (*Options, error) {
	opts, err := getOptionsByCalleeAndUserOptions(msg.CalleeServiceName(), opt...)
	if err != nil {
		return nil, err
	}
	opts = opts.clone()

	if mo, ok := opts.methods[msg.CalleeMethod()]; ok && mo.timeout != nil {
		opts.Timeout = *mo.timeout
	}

	// Set service info options with lower priority than config file options.
	// 1. opts.SelectOptions: options from config file.
	// 2. serviceInfoOpts: options from msg.
	// 3. user-provided options.
	// Initially the applying order is 1, 2, 3. (priority from low to high)
	// Now we want to change it to 2, 1, 3.
	// i.e. options from config file has higher priority than options from msg.
	serviceInfoOpts := c.getServiceInfoOptions(msg)
	opts.SelectOptions = append(serviceInfoOpts, opts.SelectOptions...)

	// The given input options have the highest priority
	// and they will override the original ones.
	for _, o := range opt {
		o(opts)
	}

	if err := opts.parseTarget(); err != nil {
		return nil, errs.NewFrameError(errs.RetClientRouteErr, err.Error())
	}
	return opts, nil
}

// getServiceInfoOptions returns service info options.
func (c *client) getServiceInfoOptions(msg codec.Msg) []selector.Option {
	if msg.Namespace() != "" {
		return []selector.Option{
			selector.WithSourceNamespace(msg.Namespace()),
			selector.WithSourceServiceName(msg.CallerServiceName()),
			selector.WithSourceEnvName(msg.EnvName()),
			selector.WithEnvTransfer(msg.EnvTransfer()),
			selector.WithSourceSetName(msg.SetName()),
		}
	}
	return nil
}

// updateMsg updates msg.
func (c *client) updateMsg(msg codec.Msg, opts *Options) {
	// Set callee service name.
	// Generally, service name is the same as the package.service defined in proto file,
	// but it can be customized by options.
	if opts.ServiceName != "" {
		// From client's perspective, caller refers to itself, callee refers to the backend service.
		msg.WithCalleeServiceName(opts.ServiceName)
	}
	if opts.endpoint == "" {
		// If endpoint is not configured, DefaultSelector (generally polaris)
		// will be used to address callee service name.
		opts.endpoint = msg.CalleeServiceName()
	}
	if opts.CalleeMethod != "" {
		msg.WithCalleeMethod(opts.CalleeMethod)
	}

	// Set metadata.
	if len(opts.MetaData) > 0 {
		msg.WithClientMetaData(c.getMetaData(msg, opts))
	}

	// Set caller service name if needed.
	if opts.CallerServiceName != "" {
		msg.WithCallerServiceName(opts.CallerServiceName)
	}
	if icodec.IsValidSerializationType(opts.SerializationType) {
		msg.WithSerializationType(opts.SerializationType)
	}
	if icodec.IsValidCompressType(opts.CompressType) && opts.CompressType != codec.CompressTypeNoop {
		msg.WithCompressType(opts.CompressType)
	}

	// Set client req head if needed.
	if opts.ReqHead != nil {
		msg.WithClientReqHead(opts.ReqHead)
	}
	// Set client rsp head if needed.
	if opts.RspHead != nil {
		msg.WithClientRspHead(opts.RspHead)
	}

	msg.WithCallType(opts.CallType)

	if opts.attachment != nil {
		setAttachment(msg, opts.attachment)
	}

	msg.WithLocalAddr(opts.localAddr)
}

// SetAttachment sets attachment to msg.
func setAttachment(msg codec.Msg, attm *attachment.Attachment) {
	cm := msg.CommonMeta()
	if cm == nil {
		cm = make(codec.CommonMeta)
		msg.WithCommonMeta(cm)
	}
	cm[attachment.ClientAttachmentKey{}] = attm
}

// getMetaData returns metadata that will be transparently transmitted to the backend service.
func (c *client) getMetaData(msg codec.Msg, opts *Options) codec.MetaData {
	md := msg.ClientMetaData()
	if md == nil {
		md = codec.MetaData{}
	}
	for k, v := range opts.MetaData {
		md[k] = v
	}
	return md
}

func (c *client) fixFilters(opts *Options) filter.ClientChain {
	if opts.DisableFilter || len(opts.Filters) == 0 {
		// All filters but selector filter are disabled.
		opts.FilterNames = append(opts.FilterNames, DefaultSelectorFilterName)
		return filter.ClientChain{selectorFilter}
	}
	if !opts.selectorFilterPosFixed {
		// Selector filter pos is not fixed, append it to the filter chain.
		opts.Filters = append(opts.Filters, selectorFilter)
		opts.FilterNames = append(opts.FilterNames, DefaultSelectorFilterName)
	}
	return opts.Filters
}

// callFunc is the function that calls the backend service with
// codec encoding/decoding and network transportation.
// Filters executed before this function are called prev filters.
// Filters executed after this function are called post filters.
func callFunc(ctx context.Context, reqBody interface{}, rspBody interface{}) (err error) {
	msg := codec.Message(ctx)
	opts := OptionsFromContext(ctx)

	// Only for compatibility with overloadctrl plugin,
	// can be deleted later.
	if !overloadctrl.IsNoop(opts.OverloadCtrl) {
		if msg.RemoteAddr() != nil {
			var token overloadctrl.Token
			token, err = opts.OverloadCtrl.Acquire(ctx, msg.RemoteAddr().String())
			if err != nil {
				return err
			}
			defer func() { token.OnResponse(ctx, err) }()
		}
	}
	defer func() { err = opts.fixTimeout(err) }()

	// Check if codec is empty, after updating msg.
	if opts.Codec == nil {
		report.ClientCodecEmpty.Incr()
		return errs.NewFrameError(errs.RetClientEncodeFail, "client: codec empty")
	}

	// Swith scope inside filter.Filter so that the client filter chain can be executed
	// even when the client is running in local scope.
	if opts.protocol == iprotocol.TRPC { // Scope is only valid for trpc protocol.
		switch opts.Scope {
		case scope.Local:
			rsp, err := inprocess.Handle(ctx, opts.ServiceName, reqBody, inprocess.Options{
				Protocol: opts.protocol,
				Codec:    opts.Codec,
			})
			if err != nil {
				return err
			}
			return ireflect.Assign(rspBody, rsp)
		case scope.All:
			rsp, err := inprocess.Handle(ctx, opts.ServiceName, reqBody, inprocess.Options{
				Protocol: opts.protocol,
				Codec:    opts.Codec,
			})
			if err == nil {
				if err := ireflect.Assign(rspBody, rsp); err == nil {
					return nil
				}
				// Fall through to do Remote call.
			}
			// Fall through to do Remote call.
		default: // Fall through.
		}
	}

	var (
		span  rpcz.Span
		ender rpcz.Ender
	)
	if rpczenable.Enabled {
		span = rpcz.SpanFromContext(ctx)
	}
	reqBuf, err := prepareRequestBuf(span, msg, reqBody, opts)
	if err != nil {
		return err
	}

	// Call backend service.
	if opts.EnableMultiplexed {
		opts.CallOptions = append(opts.CallOptions, transport.WithMsg(msg), transport.WithMultiplexed(true))
	}
	if rpczenable.Enabled {
		_, ender, ctx = rpcz.NewSpanContext(ctx, "RoundTrip")
	}
	rspBuf, err := opts.Transport.RoundTrip(ctx, reqBuf, opts.CallOptions...)
	if rpczenable.Enabled {
		ender.End()
	}
	if err != nil {
		if err == errs.ErrClientNoResponse { // Sendonly mode, no response, just return nil.
			return nil
		}
		return err
	}

	if rpczenable.Enabled {
		span.SetAttribute(rpcz.TRPCAttributeResponseSize, len(rspBuf))
		_, ender = span.NewChild("DecodeProtocolHead")
	}
	var rspBodyBuf []byte
	if opts.EnableMultiplexed {
		rspBodyBuf = rspBuf
	} else {
		rspBodyBuf, err = opts.Codec.Decode(msg, rspBuf)
	}
	if rpczenable.Enabled {
		ender.End()
	}
	if err != nil {
		return errs.NewFrameError(errs.RetClientDecodeFail, "client codec Decode: "+err.Error())
	}

	return processResponseBuf(span, msg, rspBody, rspBodyBuf, opts)
}

func prepareRequestBuf(
	span rpcz.Span,
	msg codec.Msg,
	reqBody interface{},
	opts *Options,
) ([]byte, error) {
	reqBodyBuf, err := serializeAndCompress(span, msg, reqBody, opts)
	if err != nil {
		return nil, err
	}

	// Encode the whole reqBodyBuf.
	var ender rpcz.Ender
	if rpczenable.Enabled {
		_, ender = span.NewChild("EncodeProtocolHead")
	}
	reqBuf, err := opts.Codec.Encode(msg, reqBodyBuf)
	if rpczenable.Enabled {
		ender.End()
		span.SetAttribute(rpcz.TRPCAttributeRequestSize, len(reqBuf))
	}
	if err != nil {
		return nil, errs.NewFrameError(errs.RetClientEncodeFail, "client codec Encode: "+err.Error())
	}

	return reqBuf, nil
}

func processResponseBuf(
	span rpcz.Span,
	msg codec.Msg,
	rspBody interface{},
	rspBodyBuf []byte,
	opts *Options,
) error {
	// Error from response.
	if msg.ClientRspErr() != nil {
		return msg.ClientRspErr()
	}

	if len(rspBodyBuf) == 0 {
		return nil
	}

	// Decompress.
	var ender rpcz.Ender
	if rpczenable.Enabled {
		_, ender = span.NewChild("Decompress")
	}
	compressType := msg.CompressType()
	if icodec.IsValidCompressType(opts.CurrentCompressType) {
		compressType = opts.CurrentCompressType
	}
	var err error
	if icodec.IsValidCompressType(compressType) && compressType != codec.CompressTypeNoop {
		rspBodyBuf, err = codec.Decompress(compressType, rspBodyBuf)
	}
	if rpczenable.Enabled {
		ender.End()
	}
	if err != nil {
		return errs.NewFrameError(errs.RetClientDecodeFail, "client codec Decompress: "+err.Error())
	}

	// unmarshal rspBodyBuf to rspBody.
	if rpczenable.Enabled {
		_, ender = span.NewChild("Unmarshal")
	}
	serializationType := msg.SerializationType()
	if icodec.IsValidSerializationType(opts.CurrentSerializationType) {
		serializationType = opts.CurrentSerializationType
	}
	if icodec.IsValidSerializationType(serializationType) {
		err = codec.Unmarshal(serializationType, rspBodyBuf, rspBody)
	}

	if rpczenable.Enabled {
		ender.End()
	}
	if err != nil {
		return errs.NewFrameError(errs.RetClientDecodeFail, "client codec Unmarshal: "+err.Error())
	}

	return nil
}

// serializeAndCompress serializes and compresses reqBody.
func serializeAndCompress(span rpcz.Span, msg codec.Msg, reqBody interface{}, opts *Options) ([]byte, error) {
	// Marshal reqBody into binary body.
	var ender rpcz.Ender
	if rpczenable.Enabled {
		_, ender = span.NewChild("Marshal")
	}
	serializationType := msg.SerializationType()
	if icodec.IsValidSerializationType(opts.CurrentSerializationType) {
		serializationType = opts.CurrentSerializationType
	}
	var (
		reqBodyBuf []byte
		err        error
	)
	if icodec.IsValidSerializationType(serializationType) {
		reqBodyBuf, err = codec.Marshal(serializationType, reqBody)
	}
	if rpczenable.Enabled {
		ender.End()
	}
	if err != nil {
		return nil, errs.NewFrameError(errs.RetClientEncodeFail, "client codec Marshal: "+err.Error())
	}

	// Compress.
	if rpczenable.Enabled {
		_, ender = span.NewChild("Compress")
	}
	compressType := msg.CompressType()
	if icodec.IsValidCompressType(opts.CurrentCompressType) {
		compressType = opts.CurrentCompressType
	}
	if icodec.IsValidCompressType(compressType) && compressType != codec.CompressTypeNoop {
		reqBodyBuf, err = codec.Compress(compressType, reqBodyBuf)
	}
	if rpczenable.Enabled {
		ender.End()
	}
	if err != nil {
		return nil, errs.NewFrameError(errs.RetClientEncodeFail, "client codec Compress: "+err.Error())
	}
	return reqBodyBuf, nil
}

// -------------------------------- pseudoFilter ------------------------------------- //
// pseudoFilter is the client to get node list
func pseudoFilter(ctx context.Context, req interface{}, rsp interface{}, next filter.ClientHandleFunc) error {
	return nil
}

// -------------------------------- client selector filter ------------------------------------- //

// selectorFilter is the client selector filter.
func selectorFilter(ctx context.Context, req interface{}, rsp interface{}, next filter.ClientHandleFunc) error {
	msg := codec.Message(ctx)
	opts := OptionsFromContext(ctx)
	if IsOptionsImmutable(ctx) { // Check if options are immutable.
		// The retry plugin will start multiple goroutines to process this filter concurrently,
		// and will set the options to be immutable. Therefore, the original opts cannot be modified directly,
		// and it is necessary to clone new opts.
		opts = opts.clone()
		opts.rebuildSliceCapacity()
		ctx = contextWithOptions(ctx, opts)
	}

	// Select a node of the backend service.
	node, err := selectNode(ctx, msg, opts)
	if err != nil {
		return OptionsFromContext(ctx).fixTimeout(err)
	}

	// Start to process the next filter and report.
	begin := time.Now()
	err = next(ctx, req, rsp)
	cost := time.Since(begin)

	if e := as(err); e != nil {
		e.Msg = fmt.Sprintf("%s, cost: %s", e.Msg, cost)
		opts.Selector.Report(node, cost, e)
	} else if opts.shouldErrReportToSelector(err) {
		opts.Selector.Report(node, cost, err)
	} else {
		opts.Selector.Report(node, cost, nil)
	}

	// Transmits node information back to the user.
	if addr := msg.RemoteAddr(); addr != nil {
		opts.Node.set(node, addr.String(), cost)
	} else {
		opts.Node.set(node, node.Address, cost)
	}
	return err
}

func as(err error) *errs.Error {
	if err == nil {
		return nil
	}
	e, ok := err.(*errs.Error)
	if !ok {
		return nil
	}
	if e.Type != errs.ErrorTypeFramework {
		return nil
	}
	if !(e.Code == errs.RetClientConnectFail || e.Code == errs.RetClientTimeout || e.Code == errs.RetClientNetErr) {
		return nil
	}
	return e
}

// selectNode selects a backend node by selector related options and sets the msg.
func selectNode(ctx context.Context, msg codec.Msg, opts *Options) (_ *registry.Node, err error) {
	opts.SelectOptions = append(opts.SelectOptions, selector.WithContext(ctx))
	var (
		span  rpcz.Span
		ender rpcz.Ender
	)
	if rpczenable.Enabled {
		span, ender, ctx = rpcz.NewSpanContext(ctx, "client select node")
		defer func() {
			span.SetAttribute(rpcz.TRPCAttributeError, err)
			ender.End()
		}()
	}
	node, err := getNode(opts)
	if err != nil {
		report.SelectNodeFail.Incr()
		ensureMsgCalleeInfo(msg, opts)
		return nil, err
	}
	ensureMsgRemoteAddr(msg, findFirstNonEmpty(node.Network, opts.Network), node.Address, node.ParseAddr)

	// Update msg by node config.
	opts.LoadNodeConfig(node)
	msg.WithCalleeContainerName(node.ContainerName)
	msg.WithCalleeSetName(node.SetName)

	// Set current env info as environment message for transfer only if
	// env info from upstream service is not set.
	if msg.EnvTransfer() == "" {
		msg.WithEnvTransfer(node.EnvKey)
	}

	// If service router is disabled, env info should be cleared.
	if opts.DisableServiceRouter {
		msg.WithEnvTransfer("")
	}

	// Selector might block for a while, need to check if ctx is still available.
	if ctx.Err() == context.Canceled {
		return nil, errs.NewFrameError(errs.RetClientCanceled,
			"selector canceled after Select: "+ctx.Err().Error())
	}
	if ctx.Err() == context.DeadlineExceeded {
		return nil, errs.NewFrameError(errs.RetClientTimeout,
			"selector timeout after Select: "+ctx.Err().Error())
	}

	return node, nil
}

func getNode(opts *Options) (*registry.Node, error) {
	// Select node.
	node, err := opts.Selector.Select(opts.endpoint, opts.SelectOptions...)
	if err != nil {
		return nil, errs.NewFrameError(errs.RetClientRouteErr, "client Select: "+err.Error())
	}
	if node.Address == "" {
		return nil, errs.NewFrameError(errs.RetClientRouteErr, fmt.Sprintf("client Select: node address empty: %+v", node))
	}
	return node, nil
}

func ensureMsgRemoteAddr(
	msg codec.Msg,
	network, address string,
	parseAddr func(network, address string) net.Addr,
) {
	// If RemoteAddr has already been set, just return.
	if msg.RemoteAddr() != nil {
		return
	}

	if parseAddr != nil {
		msg.WithRemoteAddr(parseAddr(network, address))
		return
	}

	switch network {
	case protocol.TCP, protocol.TCP4, protocol.TCP6, protocol.UDP, protocol.UDP4, protocol.UDP6:
		// Check if address can be parsed as an ip.
		host, _, err := net.SplitHostPort(address)
		if err != nil || net.ParseIP(host) == nil {
			return
		}
	}

	var addr net.Addr
	switch network {
	case protocol.TCP, protocol.TCP4, protocol.TCP6:
		addr = inet.ResolveAddress(network, address)
	case protocol.UDP, protocol.UDP4, protocol.UDP6:
		addr = inet.ResolveAddress(network, address)
	case protocol.UNIX:
		addr, _ = net.ResolveUnixAddr(network, address)
	default:
		addr = inet.ResolveAddress(protocol.TCP4, address)
	}
	msg.WithRemoteAddr(addr)
}

func ensureMsgCalleeInfo(msg codec.Msg, opts *Options) {
	if opts == nil {
		return
	}
	o := selector.Options{}
	for _, opt := range opts.SelectOptions {
		opt(&o)
	}
	if o.DestinationSetName != "" {
		msg.WithCalleeSetName(o.DestinationSetName)
	}
}

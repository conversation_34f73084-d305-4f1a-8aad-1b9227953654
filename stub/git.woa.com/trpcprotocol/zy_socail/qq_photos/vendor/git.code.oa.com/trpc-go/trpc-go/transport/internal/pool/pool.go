// Package pool provides a high-performance UDP connection pool implementation.
//
// The pool efficiently manages UDP connections per host with configurable limits.
//
// Key features:
//   - Connection reuse: Returns idle connections when available to minimize overhead.
//   - Connection lifecycle: Connections are returned to the pool on Close() rather than terminated.
//   - Automatic cleanup: Stale idle connections are removed based on configured thresholds.
//   - Thread-safe: All operations are protected by appropriate locks.
//   - Resource limits: Configurable per-host connection limits prevent resource exhaustion.
//   - Efficient cleanup: Background goroutine removes expired idle connections.
//   - Metrics: Optional metrics tracking and reporting for monitoring.
package pool

import (
	"container/list"
	"context"
	"errors"
	"fmt"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/transport/internal/dialer"
)

// Common errors returned by the connection pool.
var (
	// ErrPoolExhausted indicates that the connection pool has reached its maximum capacity and
	// cannot create new connections.
	ErrPoolExhausted = errors.New("connection pool exhausted")
)

// Config defines the tunable parameters for the UDP connection pool.
type Config struct {
	// MaxIdleConnsPerHost is the maximum number of idle connections per remote host to prevent resource waste.
	// A value of 0 means no limit.
	MaxIdleConnsPerHost int

	// MaxConnsPerHost is the maximum total connections per host to avoid overwhelming resources.
	// A value of 0 means no limit.
	MaxConnsPerHost int

	// IdleTimeout is the duration a connection may remain unused before being closed.
	// Must be positive.
	IdleTimeout time.Duration

	// CleanupInterval is the frequency of idle connection cleanup.
	// A value of 0 disables automatic cleanup.
	CleanupInterval time.Duration

	// DialTimeout is the timeout duration for new connections.
	// Must be positive.
	DialTimeout time.Duration

	// LocalAddr is the local address to bind to.
	// Empty string means system-assigned address.
	LocalAddr string

	// ConnectionMode specifies whether UDP connections should be connected or unconnected.
	ConnectionMode dialer.ConnectionMode

	// MetricsInterval is the frequency of metrics reporting.
	// A value of 0 disables metrics reporting.
	MetricsInterval time.Duration
}

// DefaultConfig returns recommended default configuration for UDP connection pool with sensible
// values.
func DefaultConfig() Config {
	return Config{
		MaxIdleConnsPerHost: 100,                    // Keep up to 100 idle connections per host.
		IdleTimeout:         50 * time.Second,       // Close idle connections after 50s.
		CleanupInterval:     30 * time.Second,       // Run cleanup every 30s.
		DialTimeout:         200 * time.Millisecond, // Timeout new connections after 200ms.
		ConnectionMode:      dialer.Connected,       // Default to connected UDP sockets.
		MetricsInterval:     0,                      // Metrics reporting disabled by default.
	}
}

// UDPPool implements the Pool interface with per-host connection management and cleanup.
type UDPPool struct {
	// mu protects access to the hosts map.
	mu sync.RWMutex

	// hosts stores connection pools keyed by "network:address".
	hosts map[string]*hostPool

	// config holds all pool configuration parameters.
	config Config

	// done signals cleanup goroutine to stop.
	done chan struct{}

	// metrics tracks pool usage statistics.
	metrics struct {
		// Align atomic fields to 64-bit boundary for 32-bit systems
		_ [4]byte // padding for 32-bit systems.
		// reuseCount is the total number of connections reused from idle list.
		reuseCount uint64
		// newConnCount is the total number of new connections created.
		newConnCount uint64
		// idleTimeoutCount is the total number of connections closed due to idle timeout.
		idleTimeoutCount uint64
		// poolFullCount is the total number of connections closed due to pool full.
		poolFullCount uint64
		// returnToIdleCount is the total number of connections returned to idle list on Close().
		returnToIdleCount uint64
		// actualCloseCount is the total number of connections actually closed on Close().
		actualCloseCount uint64
		// idleConnsCount is the total number of idle connections.
		idleConnsCount uint64
	}
}

// NewPool creates and initializes a UDP connection pool with automatic cleanup if configured.
func NewPool(config Config) (*UDPPool, error) {
	// Validate all configuration parameters before proceeding.
	if err := validateConfig(&config); err != nil {
		return nil, err
	}
	p := &UDPPool{
		hosts:  make(map[string]*hostPool),
		config: config,
		done:   make(chan struct{}),
	}
	// Start the cleanup goroutine if an interval is configured.
	if config.CleanupInterval > 0 {
		go p.cleanupLoop()
	}
	// Start metrics reporting if enabled.
	if config.MetricsInterval > 0 {
		go p.metricsLoop()
	}
	return p, nil
}

// Get retrieves a connection from the pool or creates a new one using the provided dial options.
func (p *UDPPool) Get(ctx context.Context, opts dialer.DialOptions) (conn net.PacketConn, err error) {
	// Handle context cancellation.
	select {
	case <-ctx.Done():
		if ctx.Err() == context.Canceled {
			return nil, errs.WrapFrameError(ctx.Err(), errs.RetClientCanceled, "context canceled")
		}
		return nil, errs.WrapFrameError(ctx.Err(), errs.RetClientTimeout, "context timeout")
	default:
	}
	// Get context deadline and timeout.
	var (
		ctxTimeout    time.Duration
		ctxDeadline   time.Time
		isSetDeadline bool
	)
	ctxDeadline, isSetDeadline = ctx.Deadline()
	if isSetDeadline {
		ctxTimeout = time.Until(ctxDeadline)
	}
	// Fix dial timeout based on context timeout.
	opts.DialTimeout = p.fixDialTimeout(opts.DialTimeout, ctxTimeout)
	// Get or create connection.
	conn, err = p.getOrCreateConn(ctx, opts)
	if err != nil {
		return nil, err
	}
	// Set deadline if context has one.
	if isSetDeadline {
		if err := conn.SetDeadline(ctxDeadline); err != nil {
			conn.Close()
			return nil, errs.WrapFrameError(err, errs.RetClientConnectFail, "set deadline for udp connection")
		}
	}
	return conn, nil
}

func (p *UDPPool) fixDialTimeout(oldDialTimeout time.Duration, ctxTimeout time.Duration) time.Duration {
	// The connection is established using the minimum of context timeout and dialing timeout.
	dialTimeout := oldDialTimeout
	if ctxTimeout > 0 {
		if ctxTimeout < dialTimeout || dialTimeout == 0 {
			dialTimeout = ctxTimeout
		}
	}
	return dialTimeout
}

func (p *UDPPool) getOrCreateConn(ctx context.Context, opts dialer.DialOptions) (net.PacketConn, error) {
	key := opts.Network + ":" + opts.Address
	// Get or create the host pool under a write lock for thread safety.
	p.mu.Lock()
	host, ok := p.hosts[key]
	if !ok {
		host = newHostPool(p.config.MaxIdleConnsPerHost, p.config.MaxConnsPerHost,
			p.config.IdleTimeout)
		p.hosts[key] = host
	}
	p.mu.Unlock()
	host.mu.Lock()
	defer host.mu.Unlock()
	// First try to reuse an existing idle connection if available.
	if conn := p.reuseIdleConn(host); conn != nil {
		atomic.AddUint64(&p.metrics.reuseCount, 1)
		return &poolConn{
			PacketConn: conn,
			pool:       p,
			network:    opts.Network,
			addr:       opts.Address,
			key:        key,
		}, nil
	}
	// Create a new connection if we haven't hit the connection limit.
	if host.maxConns > 0 && host.totalConns >= host.maxConns {
		atomic.AddUint64(&p.metrics.poolFullCount, 1)
		return nil, ErrPoolExhausted
	}
	// Create connection with timeout from config.
	dialCtx, cancel := context.WithTimeout(ctx, p.config.DialTimeout)
	defer cancel()
	conn, err := p.createNewConn(dialCtx, opts)
	if err != nil {
		return nil, err
	}
	atomic.AddUint64(&p.metrics.newConnCount, 1)
	host.totalConns++
	return &poolConn{
		PacketConn: conn,
		pool:       p,
		network:    opts.Network,
		addr:       opts.Address,
		key:        key,
	}, nil
}

func (p *UDPPool) createNewConn(ctx context.Context, opts dialer.DialOptions) (net.PacketConn, error) {
	// Handle context cancellation.
	select {
	case <-ctx.Done():
		if ctx.Err() == context.Canceled {
			return nil, errs.WrapFrameError(ctx.Err(), errs.RetClientCanceled, "context canceled")
		}
		return nil, errs.WrapFrameError(ctx.Err(), errs.RetClientTimeout, "context timeout")
	default:
	}
	// Use the provided DialUDP function or fall back to DefaultDialUDP.
	dialFunc := opts.DialUDP
	if dialFunc == nil {
		dialFunc = dialer.DefaultDialUDP
	}
	// Override dial timeout with pool config if not set.
	if opts.DialTimeout == 0 {
		opts.DialTimeout = p.config.DialTimeout
	}
	// Override local address with pool config if not set.
	if opts.LocalAddr == "" {
		opts.LocalAddr = p.config.LocalAddr
	}
	conn, err := dialFunc(ctx, opts)
	if err != nil {
		return nil, err
	}
	return conn, nil
}

func (p *UDPPool) reuseIdleConn(host *hostPool) net.PacketConn {
	for e := host.idle.Back(); e != nil; e = host.idle.Back() {
		ic := e.Value.(*idleConn)
		host.idle.Remove(e)
		atomic.AddUint64(&p.metrics.idleConnsCount, ^uint64(0))
		// Skip and close any expired connections we encounter.
		if time.Since(ic.lastUsed) > host.idleTimeout {
			if err := ic.conn.Close(); err != nil {
				fmt.Printf("Failed to close expired idle connection: %v.\n", err)
			}
			host.totalConns--
			atomic.AddUint64(&p.metrics.idleTimeoutCount, 1)
			continue
		}
		return ic.conn
	}
	return nil
}

func (p *UDPPool) cleanupLoop() {
	ticker := time.NewTicker(p.config.CleanupInterval)
	defer ticker.Stop()
	for {
		select {
		case <-p.done:
			return
		case <-ticker.C:
			p.removeIdleConns()
		}
	}
}

func (p *UDPPool) removeIdleConns() {
	p.mu.Lock()
	defer p.mu.Unlock()
	now := time.Now()
	for _, host := range p.hosts {
		host.mu.Lock()
		// Scan the idle list from front to back checking for expired connections.
		for e := host.idle.Front(); e != nil; {
			ic := e.Value.(*idleConn)
			next := e.Next()
			// Remove and close any connections that have exceeded the idle timeout.
			if now.Sub(ic.lastUsed) > host.idleTimeout {
				if err := ic.conn.Close(); err != nil {
					fmt.Printf("Failed to close expired idle connection: %v.\n", err)
				}
				host.idle.Remove(e)
				host.totalConns--
				atomic.AddUint64(&p.metrics.idleTimeoutCount, 1)
			}
			e = next
		}
		host.mu.Unlock()
	}
}

func (p *UDPPool) metricsLoop() {
	ticker := time.NewTicker(p.config.MetricsInterval)
	defer ticker.Stop()
	for {
		select {
		case <-p.done:
			return
		case <-ticker.C:
			p.logMetrics()
		}
	}
}

func (p *UDPPool) logMetrics() {
	p.mu.RLock()
	defer p.mu.RUnlock()
	var totalIdle, totalConns int
	for _, host := range p.hosts {
		host.mu.Lock()
		totalIdle += host.idle.Len()
		totalConns += host.totalConns
		host.mu.Unlock()
	}
	log.Debugf("[UDP Pool Metrics] Hosts: %d, Total Idle: %d, Total Connections: %d, "+
		"Reuse Count: %d, New Conn Count: %d, Idle Timeout Count: %d, Pool Full Count: %d, "+
		"Return To Idle Count: %d, Actual Close Count: %d\n",
		len(p.hosts), totalIdle, totalConns,
		atomic.LoadUint64(&p.metrics.reuseCount),
		atomic.LoadUint64(&p.metrics.newConnCount),
		atomic.LoadUint64(&p.metrics.idleTimeoutCount),
		atomic.LoadUint64(&p.metrics.poolFullCount),
		atomic.LoadUint64(&p.metrics.returnToIdleCount),
		atomic.LoadUint64(&p.metrics.actualCloseCount))
}

type idleConn struct {
	// conn represents the actual network socket.
	conn net.PacketConn

	// lastUsed tracks when this connection was last active.
	lastUsed time.Time
}

type hostPool struct {
	// mu protects all fields in the hostPool structure.
	mu sync.Mutex

	// idle maintains connections using a doubly linked list for efficient insertion and removal.
	idle *list.List

	// totalConns tracks total number of connections including both idle and in-use ones.
	totalConns int

	// maxIdle limits number of allowed idle connections.
	maxIdle int

	// maxConns caps total number of connections allowed.
	maxConns int

	// idleTimeout determines how long connections can remain idle.
	idleTimeout time.Duration
}

type poolConn struct {
	// PacketConn provides the actual network functionality.
	net.PacketConn

	// pool allows returning connections to the pool.
	pool *UDPPool

	// network specifies the network protocol being used.
	network string

	// addr stores the remote endpoint information.
	addr string

	// key is the cache of network:addr string.
	key string
}

func (c *poolConn) Close() error {
	if c.PacketConn == nil {
		return nil
	}
	// Check if the host pool still exists before attempting to return the connection.
	c.pool.mu.RLock()
	host, ok := c.pool.hosts[c.key]
	c.pool.mu.RUnlock()
	if !ok {
		atomic.AddUint64(&c.pool.metrics.actualCloseCount, 1)
		return c.PacketConn.Close()
	}
	host.mu.Lock()
	defer host.mu.Unlock()
	// Close the connection if we've hit the idle connection limit.
	if host.maxIdle > 0 && host.idle.Len() >= host.maxIdle {
		atomic.AddUint64(&c.pool.metrics.poolFullCount, 1)
		atomic.AddUint64(&c.pool.metrics.actualCloseCount, 1)
		host.totalConns--
		return c.PacketConn.Close()
	}
	// Return this connection to the idle pool for future reuse.
	host.idle.PushFront(newIdleConn(c.PacketConn))
	atomic.AddUint64(&c.pool.metrics.returnToIdleCount, 1)
	atomic.AddUint64(&c.pool.metrics.idleConnsCount, 1)
	return nil
}

func (c *poolConn) GetRawConn() net.PacketConn {
	return c.PacketConn
}

func newIdleConn(conn net.PacketConn) *idleConn {
	return &idleConn{
		conn:     conn,
		lastUsed: time.Now(),
	}
}

func newHostPool(maxIdle, maxConns int, idleTimeout time.Duration) *hostPool {
	return &hostPool{
		idle:        list.New(),
		maxIdle:     maxIdle,
		maxConns:    maxConns,
		idleTimeout: idleTimeout,
	}
}

func validateConfig(config *Config) error {
	if config.MaxIdleConnsPerHost < 0 {
		return errors.New("MaxIdleConnsPerHost cannot be negative")
	}
	if config.MaxConnsPerHost < 0 {
		return errors.New("MaxConnsPerHost cannot be negative")
	}
	if config.IdleTimeout <= 0 {
		return errors.New("IdleTimeout must be positive")
	}
	if config.CleanupInterval < 0 {
		return errors.New("CleanupInterval cannot be negative")
	}
	if config.DialTimeout <= 0 {
		return errors.New("DialTimeout must be positive")
	}
	if config.MetricsInterval < 0 {
		return errors.New("MetricsInterval cannot be negative")
	}
	return nil
}

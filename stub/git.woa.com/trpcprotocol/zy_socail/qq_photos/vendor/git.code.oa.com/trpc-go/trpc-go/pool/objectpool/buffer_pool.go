package objectpool

import (
	"bytes"
	"sync"
)

// B<PERSON>er<PERSON><PERSON> represents the buffer object pool.
type BufferPool struct {
	pool sync.Pool
}

// NewBufferPool creates a new bytes.Buffer object pool.
func NewBufferPool() *BufferPool {
	return &BufferPool{
		pool: sync.Pool{
			New: func() interface{} {
				return new(bytes.Buffer)
			},
		},
	}
}

// Get takes the buffer from the pool.
func (p *BufferPool) Get() *bytes.Buffer {
	return p.pool.Get().(*bytes.Buffer)
}

// Put buffer back into the pool.
func (p *BufferPool) Put(buf *bytes.Buffer) {
	p.pool.Put(buf)
}

// Package reflect provides internal implementations for reflection.
package reflect

import (
	"errors"
	"fmt"
	"reflect"
)

// Assign assigns the src value to the dst value using reflect.
func Assign(dst, src interface{}) error {
	reqDstVal := reflect.ValueOf(dst)
	if reqDstVal.Kind() != reflect.Ptr || reqDstVal.IsNil() {
		return errors.New("req must be a non-nil pointer")
	}

	reqSrcVal := reflect.ValueOf(src)
	if reqSrcVal.Kind() == reflect.Ptr {
		reqSrcVal = reqSrcVal.Elem() // Dereference pointer to get the value.
	}

	if !reqSrcVal.Type().AssignableTo(reqDstVal.Elem().Type()) {
		return fmt.Errorf("type mismatch: req dst type: %s, req src type: %s",
			reqDstVal.Elem().Type().String(), reqSrcVal.Type().String())
	}
	reqDstVal.Elem().Set(reqSrcVal)
	return nil
}

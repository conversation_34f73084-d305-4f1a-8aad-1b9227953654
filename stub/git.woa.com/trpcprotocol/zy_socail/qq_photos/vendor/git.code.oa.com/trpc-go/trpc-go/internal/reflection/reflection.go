// Package reflection is used to avoid circular references in trpc package.
package reflection

import "git.code.oa.com/trpc-go/trpc-go/server"

var (
	// Register Registers the reflection service and to the server.Service.
	// reflection service get ServiceInfo by calling *server.Server.GetServiceInfo.
	// Register is set when the user imports the reflection package,
	// and actually called by the trpc package when the service starts.
	Register = func(server.Service, *server.Server) {}
)

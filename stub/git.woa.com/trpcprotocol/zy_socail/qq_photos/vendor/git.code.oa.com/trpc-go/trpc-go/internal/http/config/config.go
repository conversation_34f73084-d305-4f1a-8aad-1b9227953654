// Package config provides the default configuration for the HTTP transport.
package config

import "time"

const (
	// DefaultDialContextTimeout is the default timeout for dialing a new connection in HTTP transport.
	DefaultDialContextTimeout = 30 * time.Second
	// DefaultDialContextKeepAlive is the default keep-alive timeout for dialing a new connection in HTTP transport.
	DefaultDialContextKeepAlive = 30 * time.Second
	// DefaultDialContextDualStack is the default dual-stack flag for dialing a new connection in HTTP transport.
	DefaultDialContextDualStack = true
	// DefaultForceAttemptHTTP2 is the default force-attempt HTTP/2 flag for dialing a new connection in HTTP transport.
	DefaultForceAttemptHTTP2 = true
	// DefaultMaxIdleConns is the default maximum number of idle connections in HTTP transport.
	DefaultMaxIdleConns = 0
	// DefaultMaxConnsPerHost is the default maximum number of connections per host in HTTP transport.
	DefaultMaxConnsPerHost = 0
	// DefaultIdleConnTimeout is the default idle connection timeout in HTTP transport.
	DefaultIdleConnTimeout = 50 * time.Second
	// DefaultTLSHandshakeTimeout is the default TLS handshake timeout in HTTP transport.
	DefaultTLSHandshakeTimeout = 10 * time.Second
	// DefaultMaxIdleConnsPerHost is the default maximum number of idle connections per host in HTTP transport.
	DefaultMaxIdleConnsPerHost = 100
	// DefaultDisableCompression is the default disable compression flag in HTTP transport.
	DefaultDisableCompression = true
	// DefaultExpectContinueTimeout is the default expect continue timeout in HTTP transport.
	DefaultExpectContinueTimeout = time.Second
)

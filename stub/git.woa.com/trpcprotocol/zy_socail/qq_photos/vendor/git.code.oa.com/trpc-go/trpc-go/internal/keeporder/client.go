// Package keeporder provides keep order functionalities.
package keeporder

import "context"

// ClientInfo represents client-side information needed to be passed through the context.
type ClientInfo struct {
	// SendError channel is used to pass back error generated by transport send.
	SendError chan error
}

type clientInfoKey struct{}

// NewContextWithClientInfo creates a new context with client-side information embedded.
func NewContextWithClientInfo(ctx context.Context, info *ClientInfo) context.Context {
	return context.WithValue(ctx, clientInfoKey{}, info)
}

// ClientInfoFromContext returns the client-side information embedded in the context.
func ClientInfoFromContext(ctx context.Context) (*ClientInfo, bool) {
	info, ok := ctx.Value(clientInfoKey{}).(*ClientInfo)
	return info, ok
}

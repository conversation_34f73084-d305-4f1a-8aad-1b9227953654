package server

import (
	"context"
	"net"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	ikeeporder "git.code.oa.com/trpc-go/trpc-go/internal/keeporder"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.code.oa.com/trpc-go/trpc-go/overloadctrl"
	"git.code.oa.com/trpc-go/trpc-go/restful"
	"git.code.oa.com/trpc-go/trpc-go/transport"
)

// Options are server side options.
type Options struct {
	container string // container name

	Namespace   string // namespace like "Production", "Development" etc.
	EnvName     string // environment name
	SetName     string // "Set" name
	ServiceName string // service name

	Address                  string        // listen address, ip:port
	Timeout                  time.Duration // timeout for handling a request
	ReadTimeout              time.Duration // timeout for reading a request
	DisableRequestTimeout    bool          // whether to disable request timeout that inherits from upstream
	DisableKeepAlives        bool          // disables keep-alives
	DisableGracefulRestart   bool          // whether to disable graceful restart
	CurrentSerializationType int
	CurrentCompressType      int

	methods map[string]*methodOptions

	protocol string // protocol like "trpc", "http" etc.
	network  string // network like "tcp", "udp" etc.

	ServeOptions []transport.ListenServeOption
	Transport    transport.ServerTransport

	OverloadCtrl overloadctrl.OverloadController

	Registry registry.Registry
	Codec    codec.Codec

	Filters          filter.ServerChain              // filter chain
	FilterNames      []string                        // the name of filters
	StreamHandle     StreamHandle                    // server stream processing
	StreamTransport  transport.ServerStreamTransport // server stream transport plugin
	MaxWindowSize    uint32                          // max window size for server stream
	CloseWaitTime    time.Duration                   // min waiting time when closing server for wait deregister finish
	MaxCloseWaitTime time.Duration                   // max waiting time when closing server for wait requests finish
	RESTOptions      []restful.Option                // RESTful router options
	StreamFilters    StreamFilterChain

	// OnResponseObsoleted is called immediately after the framework has finished using
	// the response struct passed by the user.
	// Users can use this function to return the response and its related resources to the pool,
	// enabling better object reuse. If users choose to do so, the response struct returned by the
	// user will typically be obtained from the pool rather than being allocated directly.
	OnResponseObsoleted func(ctx context.Context, rsp interface{})
}

type methodOptions struct {
	timeout *time.Duration
}

// StreamHandle is the interface that defines server stream processing.
type StreamHandle interface {
	// StreamHandleFunc does server stream processing.
	StreamHandleFunc(ctx context.Context, sh StreamHandler, si *StreamServerInfo, req []byte) ([]byte, error)
	// Init does the initialization, mainly passing and saving Options.
	Init(opts *Options) error
}

// Option sets server options.
type Option func(*Options)

// WithNamespace returns an Option that sets namespace for server.
func WithNamespace(namespace string) Option {
	return func(o *Options) {
		o.Namespace = namespace
	}
}

// WithStreamTransport returns an Option that sets transport.ServerStreamTransport for server.
func WithStreamTransport(st transport.ServerStreamTransport) Option {
	return func(o *Options) {
		o.StreamTransport = st
	}
}

// WithEnvName returns an Option that sets environment name.
func WithEnvName(envName string) Option {
	return func(o *Options) {
		o.EnvName = envName
	}
}

// WithContainer returns an Option that sets container name.
func WithContainer(container string) Option {
	return func(o *Options) {
		o.container = container
	}
}

// WithSetName returns an Option that sets "Set" name.
func WithSetName(setName string) Option {
	return func(o *Options) {
		o.SetName = setName
	}
}

// WithServiceName returns an Option that sets service name.
func WithServiceName(s string) Option {
	return func(o *Options) {
		o.ServiceName = s
		o.ServeOptions = append(o.ServeOptions, transport.WithServiceName(s))
	}
}

// WithFilter returns an Option that adds a filter.Filter (pre or post).
func WithFilter(f interface{}) Option {
	return func(o *Options) {
		const filterName = "server.WithFilter"
		o.Filters = append(o.Filters, filter.ConvertToServerFilter(filterName, f))
		o.FilterNames = append(o.FilterNames, filterName)
	}
}

// WithNamedFilter returns an Option that adds named filter
func WithNamedFilter(name string, f filter.ServerFilter) Option {
	return func(o *Options) {
		o.Filters = append(o.Filters, f)
		o.FilterNames = append(o.FilterNames, name)
	}
}

// WithFilters returns an Option that adds a filter chain.
func WithFilters(fs []filter.ServerFilter) Option {
	return func(o *Options) {
		for _, f := range fs {
			o.Filters = append(o.Filters, f)
			o.FilterNames = append(o.FilterNames, "server.WithFilters")
		}
	}
}

// WithStreamFilter returns an Option that adds a stream filter (pre or post).
func WithStreamFilter(sf StreamFilter) Option {
	return func(o *Options) {
		o.StreamFilters = append(o.StreamFilters, sf)
	}
}

// WithStreamFilters returns an Option that adds a stream filter chain.
func WithStreamFilters(sfs ...StreamFilter) Option {
	return func(o *Options) {
		o.StreamFilters = append(o.StreamFilters, sfs...)
	}
}

// WithAddress returns an Option that sets address (ip:port or :port).
func WithAddress(s string) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithListenAddress(s))
		o.Address = s
	}
}

// WithTLS returns an Option that sets TLS certificate files' path.
// The input param certFile represents server certificate.
// The input param keyFile represents server private key.
// The input param caFile represents CA certificate, which is used for client-to-server authentication(mTLS).
// If cafile is empty, no client validation.
// Also, caFile="root" means local ca file would be used to validate client.
// All certificates must be X.509 certificates.
func WithTLS(certFile, keyFile, caFile string) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithServeTLS(certFile, keyFile, caFile))
	}
}

// WithNetwork returns an Option that sets network, tcp by default.
func WithNetwork(s string) Option {
	return func(o *Options) {
		o.network = s
		o.ServeOptions = append(o.ServeOptions, transport.WithListenNetwork(s))
	}
}

// WithListener returns an Option that sets net.Listener for accept, read/write op customization.
func WithListener(lis net.Listener) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithListener(lis))
	}
}

// WithServerAsync returns an Option that sets whether to enable server async or not.
// See: https://git.woa.com/trpc-go/trpc-go/issues/113
func WithServerAsync(serverAsync bool) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithServerAsync(serverAsync))
	}
}

// WithKeepOrderPreDecodeExtractor returns a ListenServeOption which enables the keep order feature
// by providing pre-decoding extractor.
//
// By providing the pre-decoding extractor, a keep-order key will be extracted from the decoding result
// or the raw binary request body.
// Requests sharing the same keep-order key are processed serially within the same group.
// Requests from different groups, identified by different keys, are processed in parallel.
//
// The default value is nil (do not keep order).
func WithKeepOrderPreDecodeExtractor(preDecodeExtractor ikeeporder.PreDecodeExtractor) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithKeepOrderPreDecodeExtractor(preDecodeExtractor))
	}
}

// WithKeepOrderPreUnmarshalExtractor returns a ListenServeOption which enables the keep order feature
// by providing pre-unmarshalling extractor.
//
// By providing the pre-unmarshalling extractor, a keep-order key will be extracted from the unmarshalled request.
// Requests sharing the same keep-order key are processed serially within the same group.
// Requests from different groups, identified by different keys, are processed in parallel.
//
// The default value is nil (do not keep order).
func WithKeepOrderPreUnmarshalExtractor(preUnmarshalExtractor ikeeporder.PreUnmarshalExtractor) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithKeepOrderPreUnmarshalExtractor(preUnmarshalExtractor))
	}
}

// WithOrderedGroups returns a ListenServeOption which specifies the groups to use for order-keeping.
func WithOrderedGroups(groups ikeeporder.OrderedGroups) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithOrderedGroups(groups))
	}
}

// WithWritev returns an Option that sets whether to enable writev or not.
func WithWritev(writev bool) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithWritev(writev))
	}
}

// WithMaxRoutines returns an Option that sets max number of goroutines.
// It only works for server async mode.
// MaxRoutines should be set to twice as expected number of routines (can be calculated by expected QPS),
// and larger than MAXPROCS.
// If MaxRoutines is not set or set to 0, it will be set to (1<<31 - 1).
// Requests exceeding MaxRoutines will be queued. Prolonged overages may lead to OOM!
// MaxRoutines is not the solution to alleviate server overloading.
func WithMaxRoutines(routines int) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithMaxRoutines(routines))
	}
}

// WithTimeout returns an Option that sets timeout for handling a request.
func WithTimeout(t time.Duration) Option {
	return func(o *Options) {
		o.Timeout = t
	}
}

// WithReadTimeout returns an Option that sets timeout for reading a request.
func WithReadTimeout(t time.Duration) Option {
	return func(o *Options) {
		o.ReadTimeout = t
		o.ServeOptions = append(o.ServeOptions, transport.WithServerReadTimeout(t))
	}
}

// WithMethodTimeout returns an Options that sets timeout for handling the method.
func WithMethodTimeout(method string, timeout time.Duration) Option {
	return func(o *Options) {
		if mo, ok := o.methods[method]; ok {
			mo.timeout = &timeout
		} else {
			o.methods[method] = &methodOptions{timeout: &timeout}
		}
	}
}

// WithDisableRequestTimeout returns an Option that disables timeout for handling requests.
func WithDisableRequestTimeout(disable bool) Option {
	return func(o *Options) {
		o.DisableRequestTimeout = disable
	}
}

// WithRegistry returns an Option that sets registry.Registry.
// One service, one registry.Registry.
func WithRegistry(r registry.Registry) Option {
	return func(o *Options) {
		o.Registry = r
	}
}

// WithTransport returns an Option that sets transport.ServerTransport.
func WithTransport(t transport.ServerTransport) Option {
	return func(o *Options) {
		if t != nil {
			o.Transport = t
		}
	}
}

// WithProtocol returns an Option that sets protocol of service.
// This Option also sets framerbuilder and codec plugin.
func WithProtocol(s string) Option {
	return func(o *Options) {
		o.protocol = s
		o.Codec = codec.GetServer(s)
		fb := transport.GetFramerBuilder(s)
		if fb != nil {
			o.ServeOptions = append(o.ServeOptions, transport.WithServerFramerBuilder(fb))
		}
		trans := transport.GetServerTransport(s)
		if trans != nil {
			o.Transport = trans
		}
	}
}

// WithHandler returns an Option that sets transport.Handler (service itself by default).
func WithHandler(h transport.Handler) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithHandler(h))
	}
}

// WithCurrentSerializationType returns an Option that sets current serialization type.
// It's often used for transparent proxy without serialization.
// If current serialization type is not set, serialization type will be determined by
// serialization field of request protocol.
func WithCurrentSerializationType(t int) Option {
	return func(o *Options) {
		o.CurrentSerializationType = t
	}
}

// WithCurrentCompressType returns an Option that sets current compress type.
func WithCurrentCompressType(t int) Option {
	return func(o *Options) {
		o.CurrentCompressType = t
	}
}

// WithMaxWindowSize returns an Option that sets max window size for server stream.
func WithMaxWindowSize(w uint32) Option {
	return func(o *Options) {
		o.MaxWindowSize = w
	}
}

// WithCloseWaitTime returns an Option that sets min waiting time when close service.
// It's used for service's graceful restart.
// Default: 0ms, max: 10s.
func WithCloseWaitTime(t time.Duration) Option {
	return func(o *Options) {
		o.CloseWaitTime = t
	}
}

// WithMaxCloseWaitTime returns an Option that sets max waiting time when close service.
// It's used for wait requests finish.
// Default: 0ms.
func WithMaxCloseWaitTime(t time.Duration) Option {
	return func(o *Options) {
		o.MaxCloseWaitTime = t
	}
}

// WithDisableGracefulRestart returns an Option that sets whether enable graceful restart or not.
// It is no use for windows, because graceful restart is not supported on windows.
func WithDisableGracefulRestart(disable bool) Option {
	return func(o *Options) {
		o.DisableGracefulRestart = disable
	}
}

// WithRESTOptions returns an Option that sets RESTful router options.
func WithRESTOptions(opts ...restful.Option) Option {
	return func(o *Options) {
		o.RESTOptions = append(o.RESTOptions, opts...)
	}
}

// WithOverloadCtrl returns an Option that sets overloadctrl.OverloadController.
func WithOverloadCtrl(oc overloadctrl.OverloadController) Option {
	return func(o *Options) {
		o.OverloadCtrl = oc
	}
}

// WithIdleTimeout returns an Option that sets idle connection timeout.
// Notice: it doesn't work for server streaming.
func WithIdleTimeout(t time.Duration) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithServerIdleTimeout(t))
	}
}

// WithDisableKeepAlives returns an Option that disables keep-alives.
func WithDisableKeepAlives(disable bool) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithDisableKeepAlives(disable))
	}
}

// WithOnResponseObsoleted returns an Option that sets OnResponseObsoleted function.
// OnResponseObsoleted is called immediately after the framework has finished using
// the response struct passed by the user.
// Users can use this function to return the response and its related resources to the pool,
// enabling better object reuse. If users choose to do so, the response struct returned by the
// user will typically be obtained from the pool rather than being allocated directly.
func WithOnResponseObsoleted(f func(ctx context.Context, rsp interface{})) Option {
	return func(o *Options) {
		o.OnResponseObsoleted = f
	}
}

// WithServiceOption returns an Option that sets Option by service name.
func WithServiceOption(serviceName string, opt Option) Option {
	return func(o *Options) {
		if o.ServiceName == serviceName {
			opt(o)
		}
	}
}

// WithProfilerTagger returns an Option that assigns tags to goroutine.
// This allows for more detailed filtering in pprof CPU statistics based on different labels.
func WithProfilerTagger(t ProfilerTagger) Option {
	return func(o *Options) {
		o.Filters = append(filter.ServerChain{profilerTaggerFilter(t)}, o.Filters...)
		o.FilterNames = append([]string{"profiler_tagger_filter"}, o.FilterNames...)
	}
}

// WithStreamProfilerTagger returns an Option that assigns tags to goroutine for stream service.
// This allows for more detailed filtering in pprof CPU statistics based on different labels.
func WithStreamProfilerTagger(t StreamProfilerTagger) Option {
	return func(o *Options) {
		o.StreamFilters = append(StreamFilterChain{streamProfilerTaggerFilter(t)}, o.StreamFilters...)
	}
}

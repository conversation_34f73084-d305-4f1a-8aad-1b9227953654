branch:
  trunk_name: "master"
  branch_type_A:
    tag:
      pattern: "v${versionnumber}"
      versionnumber: "{Major-version}.{Feature-version}.{Fix-version}"

artifact:
  - path: "/"
    artifact_name : "go_reuseport"
    dependence_conf: "go.mod"
    repository_url: "http://git.code.oa.com/trpc-go/go_reuseport"

source:
  test_source:
    filepath_regex: [".*_test.go$"]
  auto_generate_source:
    filepath_regex: [".*.pb.go$", ".*.trpc.go$"]

code_review:
  restrict_labels: ["CR-编程规范", "CR-业务逻辑","CR-边界逻辑","CR-代码架构","CR-性能影响","CR-安全性","CR-可测试性","CR-可读性"]
  reviewers :  ["tensorchen"]
  necessary_reviewers :  ["tensorchen"]

file :
  - path: "/.*"
    owners :  ["tensorchen"]
    owner_rule: 1
    code_review:
      reviewers :  ["tensorchen"]
      necessary_reviewers :  ["tensorchen"]
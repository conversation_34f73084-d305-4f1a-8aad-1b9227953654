// Package safejob provides functions to call job in a concurrent-safe manner.
package safejob

// Job defines the interface that can call job multiple times and ensure concurrent safety.
type Job interface {
	// Begin sets the start entry of the job.
	Begin() bool

	// End sets the end entry of the job.
	End()

	// Close closes the job. After closed, the job can't be executed anymore.
	Close()

	// Closed returns whether the job is closed.
	Closed() bool
}

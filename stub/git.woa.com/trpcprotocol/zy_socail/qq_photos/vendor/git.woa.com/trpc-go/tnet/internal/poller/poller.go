// Package poller provides event driven polling system to monitor file description events.
package poller

import "fmt"

// GoschedAfterEvent decides whether to call runtime.Gosched() after processing of each event
// during epoll waiting handling.
// This global variable can only be changed inside func init().
var GoschedAfterEvent bool

// Event defines the operation of poll.Control.
type Event int

// String implements fmt.Stringer.
func (e Event) String() string {
	switch e {
	case Readable:
		return "Readable"
	case ModReadable:
		return "ModReadable"
	case Writable:
		return "Writeable"
	case ModWritable:
		return "ModWriteable"
	case ReadWriteable:
		return "ReadWriteable"
	case ModReadWriteable:
		return "ModReadWriteable"
	case Detach:
		return "Detach"
	default:
		return fmt.Sprintf("Event(%d)", e)
	}
}

// Job function is defined for jobs.
type Job func() error

// Constants for PollEvents.
const (
	Readable Event = iota
	ModReadable
	Writable
	ModWritable
	ReadWriteable
	ModReadWriteable
	Detach
)

// Poller monitors file descriptor, calls Desc callbacks according to specific events.
type Poller interface {
	// Wait will poll all the registered Desc, and trigger the event callback
	// specified by the Desc.
	Wait() error

	// Close closes the poller and stops Wait().
	Close() error

	// Trigger is used to trigger the poller to weak up from Wait(), each
	// Poller maintains a job queue, and does all the jobs after it wakes up.
	Trigger(Job) error

	// Control registers an event of Desc, which is defined by Event.
	Control(*Desc, Event) error
}

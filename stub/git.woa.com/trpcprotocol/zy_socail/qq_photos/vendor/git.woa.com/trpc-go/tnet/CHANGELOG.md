# Change Log

## [v0.1.0](https://git.woa.com/trpc-go/tnet/tree/v0.1.0) (2024-12-19)

### Bug Fixes

- systype: do not add pad in 32-bit (!245)

## [v0.0.20](https://git.woa.com/trpc-go/tnet/tree/v0.0.20) (2024-10-22)

### Bug Fixes

- udp: fix udp write buffer without skip and release (!241)

### Features

- lsc: fix typo and refactor for readability (!241)
- lsc: fix some typos and grammar errors (!239)
- examples: return EAGAIN error for combined case (!219)

## [v0.0.19](https://git.woa.com/trpc-go/tnet/tree/v0.0.19) (2024-09-03)

### Bug Fixes

- poller: always do the free desc operation during handling (!224)
- tnet: add consistency check for laddr and raddr (!230)
- udp: trigger service close after all conns are closed (!233)

### Features

- tnet: support exact udp buffer for bsd (!234)
- tnet: support alloc exact-sized buffer for UDP packets (!229)

## [v0.0.18](https://git.woa.com/trpc-go/tnet/tree/v0.0.18) (2024-07-12)

### Bug Fixes

- asynctimer: delay should updated upon adding (!231)

### Features

- all: upgrade x/sys to v0.21.0 (!225)
- tcpconn: support disabling idletimeout (!223)

## [v0.0.17](https://git.woa.com/trpc-go/tnet/tree/v0.0.17) (2024-04-15)

### Bug Fixes

- tcpconn: check negative idle timeout to prevent unexpected behaviour (!221)

## [v0.0.16](https://git.woa.com/trpc-go/tnet/tree/v0.0.16) (2023-09-19)

### Bug Fixes

- poller: add multi-arch for epoll event data (!208)

## [v0.0.15](https://git.woa.com/trpc-go/tnet/tree/v0.0.15) (2023-08-02)

### Features

- postpone: improve postpone strategy for tls (!197)

### Bug Fixes

- poller: fix desc race condition (!203)
- tls: fix onRequest not triggering (!198)
- test: fix unit test failed (!200, !206)

## [v0.0.14](https://git.woa.com/trpc-go/tnet/tree/v0.0.14) (2023-07-19)

### Features

- tls: support SetOnRequest, SetOnClosed and IsActive (!195)
- internal/cache/systype: add build tag for loong64 platform (!194)

## [v0.0.13](https://git.woa.com/trpc-go/tnet/tree/v0.0.13) (2023-07-10)

### Bug Fixes

- internal/poller: ensure each descriptor is only appended once (!192)
- tcpservice: check tcp service onRead/onHup nil pointer (!191)
- tcpconn: check nil pointer for on hup (!190)

## [v0.0.12](https://git.woa.com/trpc-go/tnet/tree/v0.0.12) (2023-06-30)

### Features

- internal/poller: add ignore task error to poller manager (!188)
- log: provide customized logging package (!187)
- accept: pass non block flag to accept4 (!185)
- tcp{service|listener}: wrap more information into error message (!183)
- listener: make assertion to check more temporary errors and do not spin on them (!182)

### Refactoring

- lsc: improve code comments and reduce duplication (!180)

## [v0.0.11](https://git.woa.com/trpc-go/tnet/tree/v0.0.11) (2023-04-18)

### Features

- extensions/websocket: provides lock for concurrent write (!175)
- extensions/websocket return message type for errors (!176)
- extensions/websocket: provide with handshake context (!174)
- extensions/websocket: add local and remote address into context before upgrade (!172, !173)

### Bug Fixes

- poller: on hup should not be an exclusive case (!177)

## [v0.0.10](https://git.woa.com/trpc-go/tnet/tree/v0.0.10) (2023-04-03)

### Bug Fixes

- {tcp|udp}conn: Report error when conn is of incorrect type or nil pointer (!169)
- examples: Nonblocking handler should return EAGAIN errors and check the full packet length (!168)

## [v0.0.9](https://git.woa.com/trpc-go/tnet/tree/v0.0.9) (2023-02-23)

### Features

- Report statistics only after listen or dial (!164)

### Bug Fixes

- Sort the reported statistics first to ensure the consistency of multiple reports (!165)
- Compilation failure on 32 bit architecture (!162)
- Change picture of readme (!163)

## [v0.0.8](https://git.woa.com/trpc-go/tnet/tree/v0.0.8) (2023-02-13)

### Bug Fixes

- Fix compilation failure on linux/arm64(!160)

## [v0.0.7](https://git.woa.com/trpc-go/tnet/tree/v0.0.7) (2023-02-08)

### Features

- Update README(!154)

### Bug Fixes

- Add build constraints '// +build' to improve the readability of build failure information(!158)
- Fix "resource temporarily unavailable" appears occasionally when writing(!156)

## 0.0.6 (2022-11-08)

### Features

- Set FD_CLOEXEC flag for tcp connection fds
- Log number of pollers to notify user
- Optimizations:
  - Writev iovec length should be larger
  - Reduce unnecessary fmt.Sprintf
- TLS support onOpened and onClosed

### Bug Fixes

- SetIdleTimeout should accept a timeout of 0

## 0.0.5 (2022-07-15)

### Bug Fixes

- Patch the previous tag with correct tnet version in the hard code

## 0.0.4 (2022-07-15)

### Features

- Auto switch for flush write
- Relocate package tls
- Improve test package readability with package name as xx_test
- Optimizations:
  - Reset wnode when read length of the buffer is zero to reduce overhead of addNode
  - Use pre-allocated iovec to reduce the pressure of sync.Pool
  - Reduce overhead of byte slice and node retrieval when copy is a necessity for safe write
  - Reduce overhead of node alloc when safewrite = false
  - Auto adjust node buffer size to reduce overhead of allocation
  - Reduce memory usage of iovec under massive connections

### Bug Fixes

- Free input/output buffer in connection's close method
- Return error from tcpOnRead instead of calling Close directly
- Wrap epoll_ctl mod error with "connection may be closed"

## 0.0.3 (2022-05-25)

### Features

- Support TLS extension
- Support Websocket extension
- Support buffer auto clean up according to the number of connections
- Provide safe write option for tcp connection
- Add stat report
- Optimizations:
  - Use RawSyscall instead of Syscall and set TCP_NODELAY for TCP socket
  - Prevent allocating memory twice during reading, reduce delay and memory allocation

## 0.0.2 (2022-04-18)

### Features

- Support Conn/PacketConn flush and UDPService flush option
- Provide submit api for task pool  
- Support UDP reuseport  

### Bug Fixes

- Fix race condition problem

## v0.0.1 (2022-01-13)

### Features

- Support TCP, UDP
- Support IPv4, IPv6  
- Provide blocking/nonblocking Read/Write API
- Provide batch system calls: ReadV/WriteV
- Support both server and client programming
- Support Linux / Mac OS

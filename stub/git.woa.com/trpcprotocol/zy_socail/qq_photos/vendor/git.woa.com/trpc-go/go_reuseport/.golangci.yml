run:
  issues-exit-code: 1 #Default
  tests: true #Default

linters:
  enable:
    - misspell
    - gofmt
    - golint
    - unused
    - ineffassign
    - deadcode
    - varcheck
    - goimports
    - golint
    - gofmt
    - funlen
    - goconst
    - gocyclo
    - structcheck
    - typecheck
    - gosimple
    - govet
    - lll
    - rowserrcheck
    - unused
    - gocyclo
  disable:
    - staticcheck
    - errcheck
    - bodyclose

issues:
  exclude-rules:
    - path: _test\.go
      text: "context.Context should be the first parameter of a function"
      linters:
        - golint
    - path: _test\.go
      text: "exported func.*returns unexported type.*which can be annoying to use"
      linters:
        - golint

linters-settings:
  goimports:
    local-prefixes: git.code.oa.com/trpc-go/go_reuseport/
  funlen:
    lines: 80
    statements: 80
  goconst:
    min-len: 2
    min-occurrences: 2
  gocyclo:
    min-complexity: 10
  golint:
    min-confidence: 0
  govet:
    check-shadowing: true
  lll:
    line-length: 120
  errcheck:
    check-type-assertions: true
  misspell:
    locale: US
    ignore-words:
      - cancelled

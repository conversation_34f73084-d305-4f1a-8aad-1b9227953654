package tnet

import (
	"git.woa.com/trpc-go/tnet/metrics"
	"github.com/panjf2000/ants/v2"
)

var (
	maxRoutines = 0 // meaning INT32_MAX.
	sysPool, _  = ants.NewPoolWithFunc(maxR<PERSON><PERSON>, taskHandler)
	usrPool, _  = ants.NewPool(maxRoutines)
)

func taskHandler(v interface{}) {
	switch conn := v.(type) {
	case *tcpconn:
		tcpAsyncHandler(conn)
	case *udpconn:
		udpAsyncHandler(conn)
	}
}

func doTask(args interface{}) error {
	metrics.Add(metrics.TaskAssigned, 1)
	return sysPool.Invoke(args)
}

// Submit submits a task to usrPool.
//
// Users can use this API to submit a task to
// the default user business goroutine pool.
func Submit(task func()) error {
	return usrPool.Submit(task)
}

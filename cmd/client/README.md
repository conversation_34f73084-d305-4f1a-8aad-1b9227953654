# QzonePhotoTool 客户端工具

这是一个用于批量处理QQ空间照片的客户端工具，支持大规模UID批处理和优雅停止功能。

## 功能特性

- **批量处理**: 支持从文件读取大量UID并分批处理
- **并发控制**: 可配置同时运行的批处理任务数量
- **优雅停止**: 支持SIGINT/SIGTERM信号优雅停止
- **进度跟踪**: 实时显示处理进度和统计信息
- **结果输出**: 将处理结果以JSON格式保存到文件
- **断点续传**: 支持从指定行开始处理
- **错误处理**: 完善的错误处理和重试机制

## 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `-addr` | `127.0.0.1:6610` | 服务器地址，格式：ip:port |
| `-method` | `RawFixTool` | 调用的方法名 |
| `-targetFile` | `./uids.txt` | UID列表文件路径 |
| `-outputFile` | `./results.txt` | 输出结果文件路径 |
| `-concurrency` | `40` | 传入server的并发数 |
| `-batchSize` | `5000` | 每批处理的UID数量 |
| `-maxTaskCount` | `2` | 同时存在的批处理任务数 |
| `-startAt` | `1` | 从目标文件的第几行开始处理 |
| `-startGap` | `500` | 每批处理间隔时间（毫秒） |
| `-timeout` | `300` | 超时时间（秒） |
| `-protocol` | `trpc` | 协议类型：trpc 或 http |

## 使用方法

### 1. 准备UID文件

创建一个包含UID的文本文件，每行可以包含一个或多个UID（逗号分隔）：

```
185333337
1292758411,1234567890
9876543210
```

### 2. 运行客户端

```bash
# 基本用法
./client_tool -targetFile=uids.txt -outputFile=results.txt

# 自定义参数
./client_tool \
  -addr=*************:6610 \
  -targetFile=my_uids.txt \
  -outputFile=my_results.txt \
  -concurrency=50 \
  -batchSize=1000 \
  -maxTaskCount=3 \
  -startGap=1000

# 从第100行开始处理
./client_tool -targetFile=uids.txt -startAt=100

# 使用HTTP协议
./client_tool -protocol=http -addr=127.0.0.1:8080
```

### 3. 优雅停止

程序运行时，可以通过以下方式优雅停止：

```bash
# 发送SIGINT信号（Ctrl+C）
kill -INT <pid>

# 发送SIGTERM信号
kill -TERM <pid>
```

## 输出格式

结果文件中每行是一个JSON对象，包含以下字段：

```json
{
  "batch_id": 1,
  "start_line": 1,
  "end_line": 100,
  "request_id": 12345,
  "processed_uids": 5000,
  "error_photos": [
    {
      "uin": "185333337",
      "albumId": "album123",
      "lloc": "lloc456"
    }
  ],
  "unprocessed_uids": [1234567890],
  "unprocessed_photos": [
    {
      "uin": "185333337",
      "albumId": "album123",
      "lloc": "lloc456",
      "bigPicUrl": "https://...",
      "rawPicUrl": "https://..."
    }
  ],
  "processing_time": "30s",
  "is_completed": true,
  "error": ""
}
```

## 日志输出

程序会输出详细的日志信息，包括：

- 批处理启动信息
- 每批次的处理进度
- 错误和警告信息
- 最终统计结果

示例日志：
```
INFO 开始批处理，参数: addr=127.0.0.1:6610, targetFile=./uids.txt, ...
INFO 总共读取到 10000 个UID，分为 2 批
INFO 开始处理批次 1，UID数量: 5000，行范围: 1-100
INFO 批次 1 获得RequestID: 12345
INFO 批次 1 处理完成: 错误图片=10, 未处理UID=5, 未处理图片=20
INFO === 批处理完成统计 ===
INFO 总批次数: 2
INFO 完成批次: 2
INFO 失败批次: 0
INFO 总UID数: 10000
INFO 错误图片总数: 25
INFO 未处理UID总数: 10
INFO 未处理图片总数: 45
INFO 总处理时间: 1m30s
INFO 总耗时: 2m0s
INFO 平均QPS: 83.33
```

## 注意事项

1. **内存使用**: 大批次处理时注意内存使用情况
2. **网络稳定性**: 确保网络连接稳定，避免超时
3. **文件权限**: 确保有读取输入文件和写入输出文件的权限
4. **服务器负载**: 根据服务器负载调整并发参数
5. **断点续传**: 处理大文件时可以使用 `-startAt` 参数从指定行继续

## 故障排除

### 常见错误

1. **连接失败**: 检查服务器地址和端口是否正确
2. **文件读取失败**: 检查文件路径和权限
3. **超时错误**: 增加 `-timeout` 参数值
4. **内存不足**: 减少 `-batchSize` 或 `-maxTaskCount` 参数

### 性能优化

1. 根据服务器性能调整 `-concurrency` 参数
2. 根据网络状况调整 `-timeout` 参数
3. 根据内存大小调整 `-batchSize` 参数
4. 使用 `-startGap` 参数控制请求频率，避免服务器过载

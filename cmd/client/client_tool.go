package main

import "flag"

var (
	serverAddr   = flag.String("addr", "127.0.0.1:6610", "服务器地址，格式：ip:port")
	method       = flag.String("method", "RawFixTool", "调用的方法名：RawFixTool")
	targetFile   = flag.String("targetFile", "./uids.txt", "用户ID列表，用,分割，会一行一行读，一行不要太大，以免浪费内存")
	outputFile   = flag.String("outputFile", "./results.txt", "输出文件，保存有问题的图片的uid albumid lloc、拉取相册列表失败的uid、拉取图片失败的图片列表")
	concurrency  = flag.Int("concurrency", 40, "传入server的并发数concurrency,是请求的参数")
	batchSize    = flag.Int("batchSize", 5000, "每批处理uid的数量")
	maxTaskCount = flag.Int("maxTaskCount", 2, "能同时存在的批处理任务数")
	startAt      = flag.Uint64("startAt", 1, "从目标文件的第几行开始处理，第一行是1")
	startGap     = flag.Int("startGap", 500, "每处理完一批后，暂停多久再开始下一批，单位毫秒")
)

// todo 要实现优雅启停
func main() {
	flag.Parse()
}

package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

var (
	serverAddr   = flag.String("addr", "127.0.0.1:6610", "服务器地址，格式：ip:port")
	method       = flag.String("method", "RawFixTool", "调用的方法名：RawFixTool")
	targetFile   = flag.String("targetFile", "./uids.txt", "用户ID列表，用,分割，会一行一行读，一行不要太大，以免浪费内存")
	outputFile   = flag.String("outputFile", "./results.txt", "输出文件，保存有问题的图片的uid albumid lloc、拉取相册列表失败的uid、拉取图片失败的图片列表")
	concurrency  = flag.Int("concurrency", 40, "传入server的并发数concurrency,是请求的参数")
	batchSize    = flag.Int("batchSize", 5000, "每批处理uid的数量")
	maxTaskCount = flag.Int("maxTaskCount", 2, "能同时存在的批处理任务数")
	startAt      = flag.Uint64("startAt", 1, "从目标文件的第几行开始处理，第一行是1")
	startGap     = flag.Int("startGap", 500, "每处理完一批后，暂停多久再开始下一批，单位毫秒")
	timeout      = flag.Int("timeout", 300000, "超时时间（秒）")
	protocol     = flag.String("protocol", "trpc", "协议类型: trpc, http")
)

// 全局变量用于优雅停止
var (
	stopSignal   = make(chan os.Signal, 1)
	stopFlag     = atomic.Bool{}
	activeTaskWg = sync.WaitGroup{}
)

// 批处理任务结果
type BatchResult struct {
	BatchID           int                        `json:"batch_id"`
	StartLine         uint64                     `json:"start_line"`
	EndLine           uint64                     `json:"end_line"`
	RequestID         uint64                     `json:"request_id"`
	ProcessedUIDs     int                        `json:"processed_uids"`
	ErrorPhotos       []*pb.PhotoErrorInfo       `json:"error_photos"`
	UnprocessedUIDs   []uint64                   `json:"unprocessed_uids"`
	UnprocessedPhotos []*pb.PhotoUnprocessedInfo `json:"unprocessed_photos"`
	ProcessingTime    time.Duration              `json:"processing_time"`
	IsCompleted       bool                       `json:"is_completed"`
	Error             string                     `json:"error,omitempty"`
}

// 统计信息
type Statistics struct {
	TotalBatches           int           `json:"total_batches"`
	CompletedBatches       int           `json:"completed_batches"`
	FailedBatches          int           `json:"failed_batches"`
	TotalUIDs              int           `json:"total_uids"`
	TotalErrorPhotos       int           `json:"total_error_photos"`
	TotalUnprocessedUIDs   int           `json:"total_unprocessed_uids"`
	TotalUnprocessedPhotos int           `json:"total_unprocessed_photos"`
	TotalProcessingTime    time.Duration `json:"total_processing_time"`
	StartTime              time.Time     `json:"start_time"`
	EndTime                time.Time     `json:"end_time"`
}

func main() {
	flag.Parse()

	// 设置信号处理
	signal.Notify(stopSignal, syscall.SIGINT, syscall.SIGTERM)

	// 启动信号监听goroutine
	go func() {
		<-stopSignal
		log.Info("收到停止信号，开始优雅停止...")
		stopFlag.Store(true)
	}()

	// 根据方法名执行不同的逻辑
	switch *method {
	case "RawFixTool":
		err := runBatchProcessing()
		if err != nil {
			log.Errorf("批处理执行失败: %v", err)
			os.Exit(1)
		}
	default:
		log.Errorf("不支持的方法: %s", *method)
		os.Exit(1)
	}

	log.Info("程序正常退出")
}

// runBatchProcessing 执行批处理逻辑
func runBatchProcessing() error {
	log.Infof("开始批处理，参数: addr=%s, targetFile=%s, outputFile=%s, concurrency=%d, batchSize=%d, maxTaskCount=%d, startAt=%d, startGap=%dms",
		*serverAddr, *targetFile, *outputFile, *concurrency, *batchSize, *maxTaskCount, *startAt, *startGap)

	// 读取UID列表
	uidBatches, totalUIDs, err := readUIDBatches(*targetFile, *batchSize, *startAt)
	if err != nil {
		return fmt.Errorf("读取UID文件失败: %v", err)
	}

	log.Infof("总共读取到 %d 个UID，分为 %d 批", totalUIDs, len(uidBatches))

	// 创建客户端
	proxy, err := createClient()
	if err != nil {
		return fmt.Errorf("创建客户端失败: %v", err)
	}

	// 创建输出文件 todo 这个这里改为发现文件存在就用append的方式，没有文件就创建文件
	outputWriter, err := createOutputWriter(*outputFile)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputWriter.Close()

	// 初始化统计信息
	stats := &Statistics{
		TotalBatches: len(uidBatches),
		TotalUIDs:    totalUIDs,
		StartTime:    time.Now(),
	}

	// 使用信号量控制并发任务数
	semaphore := make(chan struct{}, *maxTaskCount)
	resultChan := make(chan *BatchResult, len(uidBatches))

	// 启动批处理任务
	for i, batch := range uidBatches {
		if stopFlag.Load() {
			log.Info("收到停止信号，停止启动新任务")
			break
		}

		// 获取信号量
		semaphore <- struct{}{}
		activeTaskWg.Add(1)

		go func(batchID int, uids []uint64, startLine, endLine uint64) {
			defer func() {
				<-semaphore // 释放信号量
				activeTaskWg.Done()
			}()

			result := processBatch(proxy, batchID, uids, startLine, endLine)
			resultChan <- result
		}(i+1, batch.UIDs, batch.StartLine, batch.EndLine)

		// 批次间隔
		if *startGap > 0 && i < len(uidBatches)-1 {
			time.Sleep(time.Duration(*startGap) * time.Millisecond)
		}
	}

	// 等待所有任务完成
	go func() {
		activeTaskWg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for result := range resultChan {
		stats.TotalProcessingTime += result.ProcessingTime
		if result.Error != "" {
			stats.FailedBatches++
			log.Errorf("批次 %d 处理失败: %s", result.BatchID, result.Error)
		} else {
			stats.CompletedBatches++
			stats.TotalErrorPhotos += len(result.ErrorPhotos)
			stats.TotalUnprocessedUIDs += len(result.UnprocessedUIDs)
			stats.TotalUnprocessedPhotos += len(result.UnprocessedPhotos)
		}

		// 写入结果到文件
		if err := writeResult(outputWriter, result); err != nil {
			log.Errorf("写入结果失败: %v", err)
		}
	}

	stats.EndTime = time.Now()

	// 输出最终统计
	printFinalStatistics(stats)

	return nil
}

// UUIDBatch 表示一批UID
type UUIDBatch struct {
	UIDs      []uint64
	StartLine uint64
	EndLine   uint64
}

// readUIDBatches 从文件中读取UID并分批 todo数据量可能很大，我不希望一次性全部分批好。
func readUIDBatches(filename string, batchSize int, startAt uint64) ([]*UUIDBatch, int, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, 0, err
	}
	defer file.Close()

	var batches []*UUIDBatch
	var currentBatch []uint64
	var totalUIDs int
	var lineNumber uint64 = 1

	scanner := bufio.NewScanner(file)
	batchStartLine := startAt

	for scanner.Scan() {
		if lineNumber < startAt {
			lineNumber++
			continue
		}

		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			lineNumber++
			continue
		}

		// 解析这一行的UID（可能是逗号分隔的）
		uids, err := parseUIDs(line)
		if err != nil {
			log.Warnf("解析第 %d 行UID失败: %v, 跳过", lineNumber, err)
			lineNumber++
			continue
		}

		for _, uid := range uids {
			currentBatch = append(currentBatch, uid)
			totalUIDs++

			// 如果当前批次达到大小限制，创建新批次
			if len(currentBatch) >= batchSize {
				batches = append(batches, &UUIDBatch{
					UIDs:      make([]uint64, len(currentBatch)),
					StartLine: batchStartLine,
					EndLine:   lineNumber,
				})
				copy(batches[len(batches)-1].UIDs, currentBatch)
				currentBatch = currentBatch[:0] // 清空但保留容量
				batchStartLine = lineNumber + 1
			}
		}

		lineNumber++
	}

	// 处理最后一批
	if len(currentBatch) > 0 {
		batches = append(batches, &UUIDBatch{
			UIDs:      make([]uint64, len(currentBatch)),
			StartLine: batchStartLine,
			EndLine:   lineNumber - 1,
		})
		copy(batches[len(batches)-1].UIDs, currentBatch)
	}

	if err := scanner.Err(); err != nil {
		return nil, 0, err
	}

	return batches, totalUIDs, nil
}

// parseUIDs 解析UID字符串（支持逗号分隔）
func parseUIDs(uidStr string) ([]uint64, error) {
	if uidStr == "" {
		return []uint64{}, nil
	}

	parts := strings.Split(uidStr, ",")
	uids := make([]uint64, 0, len(parts))

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		uid, err := strconv.ParseUint(part, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("无效的UID '%s': %v", part, err)
		}

		uids = append(uids, uid)
	}

	return uids, nil
}

// createClient 创建TRPC客户端
func createClient() (pb.QzonePhotoToolClientProxy, error) {
	var proxy pb.QzonePhotoToolClientProxy
	if *protocol == "http" {
		proxy = pb.NewQzonePhotoToolClientProxy(
			client.WithTarget("http://"+*serverAddr),
			client.WithTimeout(time.Duration(*timeout)*time.Second),
		)
	} else {
		proxy = pb.NewQzonePhotoToolClientProxy(
			client.WithTarget("ip://"+*serverAddr),
			client.WithTimeout(time.Duration(*timeout)*time.Second),
		)
	}
	return proxy, nil
}

// createOutputWriter 创建输出文件写入器
func createOutputWriter(filename string) (*os.File, error) {
	return os.Create(filename)
}

// processBatch 处理单个批次
func processBatch(proxy pb.QzonePhotoToolClientProxy, batchID int, uids []uint64, startLine, endLine uint64) *BatchResult {
	result := &BatchResult{
		BatchID:       batchID,
		StartLine:     startLine,
		EndLine:       endLine,
		ProcessedUIDs: len(uids),
	}

	startTime := time.Now()
	defer func() {
		result.ProcessingTime = time.Since(startTime)
	}()

	log.Infof("开始处理批次 %d，UID数量: %d，行范围: %d-%d", batchID, len(uids), startLine, endLine)

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(*timeout)*time.Second)
	defer cancel()

	// 准备请求
	req := &pb.RawFixToolRequest{
		Uids:        uids,
		Concurrency: int32(*concurrency),
	}

	// 调用流式接口
	stream, err := proxy.RawFixTool(ctx, req)
	if err != nil {
		result.Error = fmt.Sprintf("调用RawFixTool失败: %v", err)
		return result
	}

	// 接收流式响应
	for {
		rsp, err := stream.Recv()

		if err == io.EOF {
			break
		}

		if err != nil {
			result.Error = fmt.Sprintf("接收响应失败: %v", err)
			return result
		}

		// 处理请求ID响应
		if rsp.GetRequestIdReply() != nil {
			result.RequestID = rsp.GetRequestIdReply().RequestId
			log.Infof("批次 %d 获得RequestID: %d", batchID, result.RequestID)
		}

		// 处理处理结果响应
		if rsp.GetProcessingResult() != nil {
			processResult := rsp.GetProcessingResult()
			result.IsCompleted = processResult.IsCompleted
			result.ErrorPhotos = processResult.ErrorList
			result.UnprocessedUIDs = processResult.UnprocessedUids
			result.UnprocessedPhotos = processResult.PhotoUnprocessedList

			log.Infof("批次 %d 处理完成: 错误图片=%d, 未处理UID=%d, 未处理图片=%d",
				batchID, len(result.ErrorPhotos), len(result.UnprocessedUIDs), len(result.UnprocessedPhotos))
		}
	}

	return result
}

// writeResult 将结果写入文件
func writeResult(writer *os.File, result *BatchResult) error {
	data, err := json.Marshal(result)
	if err != nil {
		return err
	}

	_, err = writer.WriteString(string(data) + "\n")
	return err
}

// printFinalStatistics 打印最终统计信息
func printFinalStatistics(stats *Statistics) {
	duration := stats.EndTime.Sub(stats.StartTime)

	log.Infof("=== 批处理完成统计 ===")
	log.Infof("总批次数: %d", stats.TotalBatches)
	log.Infof("完成批次: %d", stats.CompletedBatches)
	log.Infof("失败批次: %d", stats.FailedBatches)
	log.Infof("总UID数: %d", stats.TotalUIDs)
	log.Infof("错误图片总数: %d", stats.TotalErrorPhotos)
	log.Infof("未处理UID总数: %d", stats.TotalUnprocessedUIDs)
	log.Infof("未处理图片总数: %d", stats.TotalUnprocessedPhotos)
	log.Infof("总处理时间: %v", stats.TotalProcessingTime)
	log.Infof("总耗时: %v", duration)

	if stats.TotalUIDs > 0 && duration.Seconds() > 0 {
		qps := float64(stats.TotalUIDs) / duration.Seconds()
		log.Infof("平均QPS: %.2f", qps)
	}
}

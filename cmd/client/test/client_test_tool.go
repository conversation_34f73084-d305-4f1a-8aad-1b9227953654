package main

import (
	"context"
	"flag"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	pb "git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

// 命令行参数
var (
	serverAddr  = flag.String("addr", "127.0.0.1:6610", "服务器地址，格式: ip:port")
	method      = flag.String("method", "RawFixTool", "调用的方法名: RawFixTool, StopProcessing")
	uids        = flag.String("uids", "185333337,1292758411", "用户ID列表，逗号分隔")
	concurrency = flag.Int("concurrency", 2, "并发数")
	requestId   = flag.Uint64("requestId", 0, "请求ID（用于StopProcessing）")
	timeout     = flag.Int("timeout", 300000, "超时时间（秒）") //todo可能处理很久，所以这个可能会很大
	protocol    = flag.String("protocol", "trpc", "协议类型: trpc, http")
)

func main() {
	flag.Parse()

	// 创建客户端
	var proxy pb.QzonePhotoToolClientProxy
	if *protocol == "http" {
		proxy = pb.NewQzonePhotoToolClientProxy(
			client.WithTarget("http://"+*serverAddr),
			client.WithTimeout(time.Duration(*timeout)*time.Second),
		)
	} else {
		proxy = pb.NewQzonePhotoToolClientProxy(
			client.WithTarget("ip://"+*serverAddr),
			client.WithTimeout(time.Duration(*timeout)*time.Second),
		)
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(*timeout)*time.Second)
	defer cancel()

	// 根据方法名调用不同的接口
	switch *method {
	case "RawFixTool":
		callRawFixTool(ctx, proxy)
	case "StopProcessing":
		callStopProcessing(ctx, proxy)
	default:
		fmt.Printf("Error: unsupported method '%s'\n", *method)
		os.Exit(1)
	}
}

// 调用RawFixTool流式接口
func callRawFixTool(ctx context.Context, proxy pb.QzonePhotoToolClientProxy) {
	// 解析UID列表
	uidList, err := parseUIDs(*uids)
	if err != nil {
		fmt.Printf("Error parsing UIDs: %v\n", err)
		os.Exit(1)
	}

	// 准备请求
	req := &pb.RawFixToolRequest{
		Uids:        uidList,
		Concurrency: int32(*concurrency),
	}

	// 调用流式接口
	stream, err := proxy.RawFixTool(ctx, req)
	if err != nil {
		fmt.Printf("Error calling RawFixTool: %v\n", err)
		os.Exit(1)
	}
	//计时 + 计算QPS
	defer func(t time.Time, c int) {
		s := time.Since(t).Seconds()
		fmt.Printf("Total time: %.3f seconds, QPS: %.3f\n", s, float64(c)/s)
	}(time.Now(), len(uidList))

	// 接收流式响应
	for {
		rsp, err := stream.Recv()

		if err == io.EOF {
			break
		}

		if err != nil {
			fmt.Printf("Error receiving response: %v\n", err)
			os.Exit(1)
		}

		// 处理请求ID响应
		if rsp.GetRequestIdReply() != nil {
			requestId := rsp.GetRequestIdReply().RequestId
			fmt.Printf("RequestID: %d\n", requestId)
		}

		// 处理处理结果响应
		if rsp.GetProcessingResult() != nil {
			result := rsp.GetProcessingResult()
			fmt.Printf("Result: RequestID=%d, Completed=%t, Errors=%d, UnprocessedUIDs=%d, UnprocessedPhotos=%d\n",
				result.RequestId, result.IsCompleted, len(result.ErrorList),
				len(result.UnprocessedUids), len(result.PhotoUnprocessedList))

			if len(result.UnprocessedUids) > 0 {
				fmt.Printf("UnprocessedUIDs: %v\n", result.UnprocessedUids)
			}

			if len(result.ErrorList) > 0 {
				fmt.Printf("ErrorPhotos:\n")
				for i, errorInfo := range result.ErrorList {
					if i >= 5 {
						fmt.Printf("  ... and %d more\n", len(result.ErrorList)-5)
						break
					}
					fmt.Printf("  UIN=%s, AlbumID=%s, LLOC=%s\n",
						errorInfo.Uin, errorInfo.AlbumId, errorInfo.Lloc)
				}
			}

			if len(result.PhotoUnprocessedList) > 0 {
				fmt.Printf("UnprocessedPhotos:\n")
				for i, photoInfo := range result.PhotoUnprocessedList {
					if i >= 5 {
						fmt.Printf("  ... and %d more\n", len(result.PhotoUnprocessedList)-5)
						break
					}
					fmt.Printf("  UIN=%s, AlbumID=%s, LLOC=%s\n",
						photoInfo.Uin, photoInfo.AlbumId, photoInfo.Lloc)
				}
			}
		}
	}
}

// 调用StopProcessing接口
func callStopProcessing(ctx context.Context, proxy pb.QzonePhotoToolClientProxy) {
	if *requestId < 0 {
		fmt.Println("Error: StopProcessing requires -requestId parameter")
		os.Exit(1)
	}

	// 准备请求
	req := &pb.StopProcessingRequest{
		RequestId: *requestId,
	}

	// 调用停止接口
	rsp, err := proxy.StopProcessing(ctx, req)
	if err != nil {
		fmt.Printf("Error calling StopProcessing: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Stop result: Success=%t, Message=%s\n", rsp.Success, rsp.Message)
}

// 解析UID列表
func parseUIDs(uidStr string) ([]uint64, error) {
	if uidStr == "" {
		return []uint64{}, nil
	}

	parts := strings.Split(uidStr, ",")
	uids := make([]uint64, 0, len(parts))

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		uid, err := strconv.ParseUint(part, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("无效的UID '%s': %v", part, err)
		}

		uids = append(uids, uid)
	}

	return uids, nil
}

#!/bin/bash

# QzonePhotoTool 客户端示例启动脚本

# 设置默认参数
SERVER_ADDR="127.0.0.1:6610"
TARGET_FILE="example_uids.txt"
OUTPUT_FILE="results_$(date +%Y%m%d_%H%M%S).txt"
CONCURRENCY=10
BATCH_SIZE=5
MAX_TASK_COUNT=1
START_GAP=1000
TIMEOUT=300
PROTOCOL="trpc"

# 检查是否存在编译好的可执行文件
if [ ! -f "client_tool" ]; then
    echo "编译客户端工具..."
    go build -o client_tool client_tool.go
    if [ $? -ne 0 ]; then
        echo "编译失败！"
        exit 1
    fi
fi

# 检查UID文件是否存在
if [ ! -f "$TARGET_FILE" ]; then
    echo "UID文件 $TARGET_FILE 不存在！"
    echo "请创建UID文件或使用 example_uids.txt"
    exit 1
fi

echo "=== QzonePhotoTool 客户端启动 ==="
echo "服务器地址: $SERVER_ADDR"
echo "输入文件: $TARGET_FILE"
echo "输出文件: $OUTPUT_FILE"
echo "并发数: $CONCURRENCY"
echo "批次大小: $BATCH_SIZE"
echo "最大任务数: $MAX_TASK_COUNT"
echo "批次间隔: ${START_GAP}ms"
echo "超时时间: ${TIMEOUT}s"
echo "协议类型: $PROTOCOL"
echo "================================"

# 启动客户端
./client_tool \
    -addr="$SERVER_ADDR" \
    -targetFile="$TARGET_FILE" \
    -outputFile="$OUTPUT_FILE" \
    -concurrency=$CONCURRENCY \
    -batchSize=$BATCH_SIZE \
    -maxTaskCount=$MAX_TASK_COUNT \
    -startGap=$START_GAP \
    -timeout=$TIMEOUT \
    -protocol="$PROTOCOL"

echo "处理完成！结果已保存到: $OUTPUT_FILE"

#!/bin/bash

# 设置编译环境变量
export CGO_ENABLED=1
export GOOS=linux
export GOARCH=amd64

# 静态编译客户端测试工具
echo "开始静态编译客户端测试工具..."
go build \
    -a \
    -ldflags '-extldflags "-static"' \
    -tags 'netgo osusergo static_build' \
    -o client_test_tool \
    client_test_tool.go

# 验证是否为静态编译
echo "验证编译结果："
ldd client_test_tool || echo "静态编译成功"

# 显示文件大小
echo "编译后文件大小："
ls -lh client_test_tool

echo "客户端测试工具静态编译完成！"
package service

import (
	"context"
	"errors"
	"sync"
	"sync/atomic"
	"time"

	"git.woa.com/fanniliu/output_error_qq_photos/internal/handler"
	"git.woa.com/trpc-go/tnet/log"
	"git.woa.com/trpcprotocol/qzone/storage_photo"
	pb "git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

type QzonePhotoToolImpl struct {
	pb.UnimplementedQzonePhotoTool
	QzonePhotoToolHandler *handler.QzonePhotoToolHandlerImpl
	RequestManager        *RequestManager
}

type ChannelStruct struct {
	PhotoInfo *storage_photo.PhotoInfo
	Uin       uint64
}

// todo 先对传入的uid进行拉取，并计算url。然后把url输入到channel中，然后启动多个goroutine从channel中下载图片，进行处理。每次进行小批量处理。
func (this *QzonePhotoToolImpl) RawFixTool(req *pb.RawFixToolRequest, stream pb.QzonePhotoTool_RawFixToolServer) error {
	// 计算耗时
	defer func(start time.Time) {
		log.Debugf("RawFixTool cost:%v", time.Since(start))
	}(time.Now())

	//创建请求
	request := this.RequestManager.CreateRequest(stream)

	defer func() {
		this.RequestManager.RemoveRequest(request.ID)
	}()

	//并发批处理
	processCompletion := make(chan error, 1) //用于接收processRequest的完成状态
	go this.processRequest(req, request, processCompletion)
	//等待处理完成
	select {
	case <-stream.Context().Done(): //客户端通过stream.Context().Done()取消了rpc请求
		log.Debugf("RawFixTool RequestID: %v stream context done early. Reason: %v", request.ID, stream.Context().Err())
		return stream.Context().Err()
	case processError := <-processCompletion: //processRequest的goroutine处理完成
		log.Debug("RawFixTool RequestID: %v processRequest done. Result error: %v", request.ID, processError)
		return processError
	}
}

func (this *QzonePhotoToolImpl) processRequest(req *pb.RawFixToolRequest, request *Request, done chan error) {
	var finalErr error

	defer func() {
		if r := recover(); r != nil {
			log.Errorf("processRequest RequestID: %v PANIC: %v", request.ID, r)
			finalErr = errors.New("internal server error during processRequest")
		}
		done <- finalErr
	}()

	uids := req.Uids
	concurrency := req.Concurrency
	if concurrency <= 0 {
		concurrency = 1
	}

	var errorList []*pb.PhotoErrorInfo = []*pb.PhotoErrorInfo{}
	var errorListMutex sync.Mutex
	var photoUnprocessedList []*pb.PhotoUnprocessedInfo = []*pb.PhotoUnprocessedInfo{}
	var photoUnprocessedListMutex sync.Mutex
	var unprocessedUids []uint64 = []uint64{}
	var unprocessedUidsMutex sync.Mutex

	log.Debugf("processRequest RequestID:%v concurrency:%v", request.ID, concurrency)

	//第一次返回，返回请求号
	err := request.Stream.Send(&pb.RawFixToolReply{
		Response: &pb.RawFixToolReply_RequestIdReply{
			RequestIdReply: &pb.RequestIdReply{
				RequestId: request.ID,
			},
		},
	})
	if err != nil {
		log.Errorf("send requestID:%v err:%v", request.ID, err)
		finalErr = err
		return
	}
	log.Debugf("processRequest RequestID:%v RequestId reply sent.", request.ID)

	possibleErrUrlChannel := make(chan ChannelStruct, 10000)
	var producerWg sync.WaitGroup //用于等待所有生产者完成
	var consumerWg sync.WaitGroup //用于等待所有消费者完成
	index := atomic.Uint64{}      //用于存储下一个没有开始处理的uids的下标
	index.Store(0)
	//处理每个uid把图片url放入channel中
	for i := 0; i < int(concurrency); i++ {
		producerWg.Add(1)
		go func() {
			defer producerWg.Done()

			for {
				currentIndex := index.Add(1) - 1
				if currentIndex >= uint64(len(uids)) { //全部处理完毕
					return
				}

				uid := uids[currentIndex]

				possibleErrPhotos, err := this.QzonePhotoToolHandler.FindPossibleErrorPhotosByUin(uid)
				//如果uid没有被处理，加入unprocessedUids
				if err != nil {
					unprocessedUidsMutex.Lock()
					unprocessedUids = append(unprocessedUids, uid)
					unprocessedUidsMutex.Unlock()
					continue
				}

				for _, possibleErrPhoto := range possibleErrPhotos {
					possibleErrUrlChannel <- ChannelStruct{
						PhotoInfo: possibleErrPhoto,
						Uin:       uid,
					}
				}
			}
		}()
	}
	//独立的goroutine，当所有生产者完成，关闭possibleErrUrlChannel
	go func() {
		producerWg.Wait()
		log.Debugf("processRequest RequestID:%v 所有生产者已完成，关闭 possibleErrUrlChannel", request.ID)
		close(possibleErrUrlChannel)
	}()

	//多线程把图片url从channel中取出，处理
	for i := 0; i < int(concurrency); i++ {
		consumerWg.Add(1)
		go func() {
			defer consumerWg.Done()

			for {
				//todo 优雅启停，后面的全部加入unprocessedPhotos?有时间再做吧
				select {
				case <-request.Stream.Context().Done():
					// 客户端取消了请求，或连接中断。
					log.Debugf("processRequest RequestID:%v stream context done, worker stopping. Reason: %v", request.ID, request.Stream.Context().Err())
					return
				default:
					if request.StopFlag.Load() {
						log.Debugf("processRequest RequestID:%v stop", request.ID)
						return
					}
				}
				//处理完毕，done
				possibleErrPhoto, ok := <-possibleErrUrlChannel
				if !ok {
					return
				}
				//判断是否是错误图片
				errorPhoto, unprocessedPhoto, _ := this.QzonePhotoToolHandler.FindErrorPhotosFromPossibleErrorPhotos(possibleErrPhoto.Uin, []*storage_photo.PhotoInfo{possibleErrPhoto.PhotoInfo})

				if len(errorPhoto) != 0 {
					errorListMutex.Lock()
					errorList = append(errorList, errorPhoto...)
					errorListMutex.Unlock()
				}
				if len(unprocessedPhoto) != 0 {
					photoUnprocessedListMutex.Lock()
					photoUnprocessedList = append(photoUnprocessedList, unprocessedPhoto...)
					photoUnprocessedListMutex.Unlock()
				}
			}
		}()
	}

	consumerWg.Wait()

	finalSendErr := request.Stream.Send(&pb.RawFixToolReply{
		Response: &pb.RawFixToolReply_ProcessingResult{
			ProcessingResult: &pb.ProcessingResult{
				RequestId:            request.ID,
				IsCompleted:          true,
				ErrorList:            errorList,
				UnprocessedUids:      unprocessedUids,
				PhotoUnprocessedList: photoUnprocessedList,
			},
		},
	})

	if finalSendErr != nil {
		log.Errorf("processRequest RequestID:%v send error:%v", request.ID, finalSendErr)
		finalErr = finalSendErr
		return
	}
	log.Debugf("processRequest RequestID:%v final ProcessingResult reply sent.", request.ID)
}

func (this *QzonePhotoToolImpl) StopProcessing(ctx context.Context, req *pb.StopProcessingRequest) (*pb.StopProcessingReply, error) {
	success, msg := this.RequestManager.StopRequest(req.RequestId)
	if success {
		log.Debugf("StopProcessing RequestID:%v success", req.RequestId)
		return &pb.StopProcessingReply{
			Success: success,
			Message: msg,
		}, nil
	} else {
		return &pb.StopProcessingReply{
			Success: success,
			Message: msg,
		}, nil
	}
}

func NewQzonePhotoToolImpl() *QzonePhotoToolImpl {
	result := &QzonePhotoToolImpl{
		QzonePhotoToolHandler: handler.NewQzonePhotoToolHandlerImpl(),
		RequestManager:        NewRequestManager(),
	}
	return result
}

/*
请求管理器
*/
type RequestManager struct {
	nextRequestID    uint64
	activateRequests map[uint64]*Request
	mutex            sync.Mutex
}

type Request struct {
	ID       uint64
	StopFlag *atomic.Bool
	Stream   pb.QzonePhotoTool_RawFixToolServer
}

func NewRequestManager() *RequestManager {
	return &RequestManager{
		nextRequestID:    0,
		activateRequests: make(map[uint64]*Request),
	}
}

func (this *RequestManager) CreateRequest(stream pb.QzonePhotoTool_RawFixToolServer) *Request {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	requestId := this.nextRequestID
	this.nextRequestID++

	stopFlag := &atomic.Bool{}
	stopFlag.Store(false)

	request := Request{
		ID:       requestId,
		StopFlag: stopFlag,
		Stream:   stream,
	}

	this.activateRequests[requestId] = &request

	return &request
}

func (this *RequestManager) StopRequest(requestID uint64) (bool, string) {
	request, ok := this.activateRequests[requestID]
	if !ok {
		return false, "request not exist"
	}

	if request.StopFlag.Load() {
		return false, "request already stopped"
	}

	request.StopFlag.Store(true)

	return true, ""
}

func (this *RequestManager) RemoveRequest(requestID uint64) {
	this.mutex.Lock()
	defer this.mutex.Unlock()
	delete(this.activateRequests, requestID)
}
